accessLog:
  als:
    - destination:
        name: accesslog_als_0_1
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_0_1/backend/0
      http:
        requestHeaders:
          - x-client-ip-address
        responseHeaders:
          - cache-control
        responseTrailers:
          - expires
      logType: Route
      name: accesslog
      text: |
        this is a route log
      type: HTTP
    - destination:
        name: accesslog_als_0_2
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_0_2/backend/0
      logType: Route
      name: envoy-gateway-system/test
      text: |
        this is a route log
      type: TCP
    - destination:
        name: accesslog_als_1_1
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_1_1/backend/0
      http:
        requestHeaders:
          - x-client-ip-address
        responseHeaders:
          - cache-control
        responseTrailers:
          - expires
      logType: Listener
      name: accesslog
      text: |
        this is a listener log
      type: HTTP
    - destination:
        name: accesslog_als_1_2
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_1_2/backend/0
      logType: Listener
      name: envoy-gateway-system/test
      text: |
        this is a listener log
      type: TCP
    - destination:
        name: accesslog_als_2_1
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_2_1/backend/0
      http:
        requestHeaders:
          - x-client-ip-address
        responseHeaders:
          - cache-control
        responseTrailers:
          - expires
      name: accesslog
      text: |
        this is a Global log
      type: HTTP
    - destination:
        name: accesslog_als_2_2
        settings:
          - addressType: IP
            endpoints:
              - host: ***********
                port: 9090
            protocol: GRPC
            name: accesslog_als_2_2/backend/0
      name: envoy-gateway-system/test
      text: |
        this is a Global log
      type: TCP
  openTelemetry:
    - authority: otel-collector.monitoring.svc.cluster.local
      destination:
        name: accesslog_otel_0_3
        settings:
          - endpoints:
              - host: otel-collector.monitoring.svc.cluster.local
                port: 4317
            protocol: GRPC
            addressType: FQDN
            weight: 1
            name: accesslog_otel_0_3/backend/0
      logType: Route
      resources:
        k8s.cluster.name: cluster-1
      text: |
        this is a route log
    - authority: otel-collector.monitoring.svc.cluster.local
      destination:
        name: accesslog_otel_1_3
        settings:
          - endpoints:
              - host: otel-collector.monitoring.svc.cluster.local
                port: 4317
            protocol: GRPC
            addressType: FQDN
            weight: 1
            name: accesslog_otel_1_3/backend/0
      logType: Listener
      resources:
        k8s.cluster.name: cluster-1
      text: |
        this is a listener log
    - authority: otel-collector.monitoring.svc.cluster.local
      destination:
        name: accesslog_otel_2_3
        settings:
          - endpoints:
              - host: otel-collector.monitoring.svc.cluster.local
                port: 4317
            protocol: GRPC
            addressType: FQDN
            weight: 1
            name: accesslog_otel_2_3/backend/0
      resources:
        k8s.cluster.name: cluster-1
      text: |
        this is a Global log
  text:
    - format: |
        this is a route log
      logType: Route
      path: /dev/stdout
    - format: |
        this is a listener log
      logType: Listener
      path: /dev/stdout
    - format: |
        this is a Global log
      path: /dev/stdout
  json:
    - path: "/dev/stdout"
http:
  - address: 0.0.0.0
    hostnames:
      - '*'
    isHTTP2: false
    metadata:
      kind: Gateway
      name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    name: envoy-gateway/gateway-1/http
    path:
      escapedSlashesAction: UnescapeAndRedirect
      mergeSlashes: true
    port: 10080
    routes:
      - name: "direct-route"
        hostname: "*"
        destination:
          name: "direct-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "direct-route-dest/backend/0"
