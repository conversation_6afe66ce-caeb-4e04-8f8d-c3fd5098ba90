//go:build !ignore_autogenerated

// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/api/autoscaling/v2"
	corev1 "k8s.io/api/core/v1"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"sigs.k8s.io/gateway-api/apis/v1"
	"sigs.k8s.io/gateway-api/apis/v1alpha2"
	"sigs.k8s.io/gateway-api/apis/v1alpha3"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ALSEnvoyProxyAccessLog) DeepCopyInto(out *ALSEnvoyProxyAccessLog) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.LogName != nil {
		in, out := &in.LogName, &out.LogName
		*out = new(string)
		**out = **in
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(ALSEnvoyProxyHTTPAccessLogConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ALSEnvoyProxyAccessLog.
func (in *ALSEnvoyProxyAccessLog) DeepCopy() *ALSEnvoyProxyAccessLog {
	if in == nil {
		return nil
	}
	out := new(ALSEnvoyProxyAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ALSEnvoyProxyHTTPAccessLogConfig) DeepCopyInto(out *ALSEnvoyProxyHTTPAccessLogConfig) {
	*out = *in
	if in.RequestHeaders != nil {
		in, out := &in.RequestHeaders, &out.RequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseHeaders != nil {
		in, out := &in.ResponseHeaders, &out.ResponseHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseTrailers != nil {
		in, out := &in.ResponseTrailers, &out.ResponseTrailers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ALSEnvoyProxyHTTPAccessLogConfig.
func (in *ALSEnvoyProxyHTTPAccessLogConfig) DeepCopy() *ALSEnvoyProxyHTTPAccessLogConfig {
	if in == nil {
		return nil
	}
	out := new(ALSEnvoyProxyHTTPAccessLogConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *APIKeyAuth) DeepCopyInto(out *APIKeyAuth) {
	*out = *in
	if in.CredentialRefs != nil {
		in, out := &in.CredentialRefs, &out.CredentialRefs
		*out = make([]v1.SecretObjectReference, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ExtractFrom != nil {
		in, out := &in.ExtractFrom, &out.ExtractFrom
		*out = make([]*ExtractFrom, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ExtractFrom)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new APIKeyAuth.
func (in *APIKeyAuth) DeepCopy() *APIKeyAuth {
	if in == nil {
		return nil
	}
	out := new(APIKeyAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ActiveHealthCheck) DeepCopyInto(out *ActiveHealthCheck) {
	*out = *in
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.UnhealthyThreshold != nil {
		in, out := &in.UnhealthyThreshold, &out.UnhealthyThreshold
		*out = new(uint32)
		**out = **in
	}
	if in.HealthyThreshold != nil {
		in, out := &in.HealthyThreshold, &out.HealthyThreshold
		*out = new(uint32)
		**out = **in
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPActiveHealthChecker)
		(*in).DeepCopyInto(*out)
	}
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPActiveHealthChecker)
		(*in).DeepCopyInto(*out)
	}
	if in.GRPC != nil {
		in, out := &in.GRPC, &out.GRPC
		*out = new(GRPCActiveHealthChecker)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ActiveHealthCheck.
func (in *ActiveHealthCheck) DeepCopy() *ActiveHealthCheck {
	if in == nil {
		return nil
	}
	out := new(ActiveHealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ActiveHealthCheckPayload) DeepCopyInto(out *ActiveHealthCheckPayload) {
	*out = *in
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = new(string)
		**out = **in
	}
	if in.Binary != nil {
		in, out := &in.Binary, &out.Binary
		*out = make([]byte, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ActiveHealthCheckPayload.
func (in *ActiveHealthCheckPayload) DeepCopy() *ActiveHealthCheckPayload {
	if in == nil {
		return nil
	}
	out := new(ActiveHealthCheckPayload)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Authorization) DeepCopyInto(out *Authorization) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]AuthorizationRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DefaultAction != nil {
		in, out := &in.DefaultAction, &out.DefaultAction
		*out = new(AuthorizationAction)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Authorization.
func (in *Authorization) DeepCopy() *Authorization {
	if in == nil {
		return nil
	}
	out := new(Authorization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthorizationHeaderMatch) DeepCopyInto(out *AuthorizationHeaderMatch) {
	*out = *in
	if in.Values != nil {
		in, out := &in.Values, &out.Values
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthorizationHeaderMatch.
func (in *AuthorizationHeaderMatch) DeepCopy() *AuthorizationHeaderMatch {
	if in == nil {
		return nil
	}
	out := new(AuthorizationHeaderMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthorizationRule) DeepCopyInto(out *AuthorizationRule) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.Operation != nil {
		in, out := &in.Operation, &out.Operation
		*out = new(Operation)
		(*in).DeepCopyInto(*out)
	}
	in.Principal.DeepCopyInto(&out.Principal)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthorizationRule.
func (in *AuthorizationRule) DeepCopy() *AuthorizationRule {
	if in == nil {
		return nil
	}
	out := new(AuthorizationRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackOffPolicy) DeepCopyInto(out *BackOffPolicy) {
	*out = *in
	if in.BaseInterval != nil {
		in, out := &in.BaseInterval, &out.BaseInterval
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.MaxInterval != nil {
		in, out := &in.MaxInterval, &out.MaxInterval
		*out = new(metav1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackOffPolicy.
func (in *BackOffPolicy) DeepCopy() *BackOffPolicy {
	if in == nil {
		return nil
	}
	out := new(BackOffPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Backend) DeepCopyInto(out *Backend) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Backend.
func (in *Backend) DeepCopy() *Backend {
	if in == nil {
		return nil
	}
	out := new(Backend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *Backend) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendCluster) DeepCopyInto(out *BackendCluster) {
	*out = *in
	if in.BackendRef != nil {
		in, out := &in.BackendRef, &out.BackendRef
		*out = new(v1.BackendObjectReference)
		(*in).DeepCopyInto(*out)
	}
	if in.BackendRefs != nil {
		in, out := &in.BackendRefs, &out.BackendRefs
		*out = make([]BackendRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.BackendSettings != nil {
		in, out := &in.BackendSettings, &out.BackendSettings
		*out = new(ClusterSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendCluster.
func (in *BackendCluster) DeepCopy() *BackendCluster {
	if in == nil {
		return nil
	}
	out := new(BackendCluster)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendConnection) DeepCopyInto(out *BackendConnection) {
	*out = *in
	if in.BufferLimit != nil {
		in, out := &in.BufferLimit, &out.BufferLimit
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.SocketBufferLimit != nil {
		in, out := &in.SocketBufferLimit, &out.SocketBufferLimit
		x := (*in).DeepCopy()
		*out = &x
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendConnection.
func (in *BackendConnection) DeepCopy() *BackendConnection {
	if in == nil {
		return nil
	}
	out := new(BackendConnection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendEndpoint) DeepCopyInto(out *BackendEndpoint) {
	*out = *in
	if in.FQDN != nil {
		in, out := &in.FQDN, &out.FQDN
		*out = new(FQDNEndpoint)
		**out = **in
	}
	if in.IP != nil {
		in, out := &in.IP, &out.IP
		*out = new(IPEndpoint)
		**out = **in
	}
	if in.Unix != nil {
		in, out := &in.Unix, &out.Unix
		*out = new(UnixSocket)
		**out = **in
	}
	if in.Zone != nil {
		in, out := &in.Zone, &out.Zone
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendEndpoint.
func (in *BackendEndpoint) DeepCopy() *BackendEndpoint {
	if in == nil {
		return nil
	}
	out := new(BackendEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendList) DeepCopyInto(out *BackendList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Backend, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendList.
func (in *BackendList) DeepCopy() *BackendList {
	if in == nil {
		return nil
	}
	out := new(BackendList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendRef) DeepCopyInto(out *BackendRef) {
	*out = *in
	in.BackendObjectReference.DeepCopyInto(&out.BackendObjectReference)
	if in.Fallback != nil {
		in, out := &in.Fallback, &out.Fallback
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendRef.
func (in *BackendRef) DeepCopy() *BackendRef {
	if in == nil {
		return nil
	}
	out := new(BackendRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSpec) DeepCopyInto(out *BackendSpec) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(BackendType)
		**out = **in
	}
	if in.Endpoints != nil {
		in, out := &in.Endpoints, &out.Endpoints
		*out = make([]BackendEndpoint, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.AppProtocols != nil {
		in, out := &in.AppProtocols, &out.AppProtocols
		*out = make([]AppProtocolType, len(*in))
		copy(*out, *in)
	}
	if in.Fallback != nil {
		in, out := &in.Fallback, &out.Fallback
		*out = new(bool)
		**out = **in
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(BackendTLSSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.HostOverrideSettings != nil {
		in, out := &in.HostOverrideSettings, &out.HostOverrideSettings
		*out = new(HostOverrideSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSpec.
func (in *BackendSpec) DeepCopy() *BackendSpec {
	if in == nil {
		return nil
	}
	out := new(BackendSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendStatus) DeepCopyInto(out *BackendStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendStatus.
func (in *BackendStatus) DeepCopy() *BackendStatus {
	if in == nil {
		return nil
	}
	out := new(BackendStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTLSConfig) DeepCopyInto(out *BackendTLSConfig) {
	*out = *in
	if in.ClientCertificateRef != nil {
		in, out := &in.ClientCertificateRef, &out.ClientCertificateRef
		*out = new(v1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
	in.TLSSettings.DeepCopyInto(&out.TLSSettings)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTLSConfig.
func (in *BackendTLSConfig) DeepCopy() *BackendTLSConfig {
	if in == nil {
		return nil
	}
	out := new(BackendTLSConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTLSSettings) DeepCopyInto(out *BackendTLSSettings) {
	*out = *in
	if in.CACertificateRefs != nil {
		in, out := &in.CACertificateRefs, &out.CACertificateRefs
		*out = make([]v1.LocalObjectReference, len(*in))
		copy(*out, *in)
	}
	if in.WellKnownCACertificates != nil {
		in, out := &in.WellKnownCACertificates, &out.WellKnownCACertificates
		*out = new(v1alpha3.WellKnownCACertificatesType)
		**out = **in
	}
	if in.InsecureSkipVerify != nil {
		in, out := &in.InsecureSkipVerify, &out.InsecureSkipVerify
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTLSSettings.
func (in *BackendTLSSettings) DeepCopy() *BackendTLSSettings {
	if in == nil {
		return nil
	}
	out := new(BackendTLSSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTelemetry) DeepCopyInto(out *BackendTelemetry) {
	*out = *in
	if in.Tracing != nil {
		in, out := &in.Tracing, &out.Tracing
		*out = new(Tracing)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTelemetry.
func (in *BackendTelemetry) DeepCopy() *BackendTelemetry {
	if in == nil {
		return nil
	}
	out := new(BackendTelemetry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTrafficPolicy) DeepCopyInto(out *BackendTrafficPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTrafficPolicy.
func (in *BackendTrafficPolicy) DeepCopy() *BackendTrafficPolicy {
	if in == nil {
		return nil
	}
	out := new(BackendTrafficPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendTrafficPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTrafficPolicyList) DeepCopyInto(out *BackendTrafficPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]BackendTrafficPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTrafficPolicyList.
func (in *BackendTrafficPolicyList) DeepCopy() *BackendTrafficPolicyList {
	if in == nil {
		return nil
	}
	out := new(BackendTrafficPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendTrafficPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendTrafficPolicySpec) DeepCopyInto(out *BackendTrafficPolicySpec) {
	*out = *in
	in.PolicyTargetReferences.DeepCopyInto(&out.PolicyTargetReferences)
	in.ClusterSettings.DeepCopyInto(&out.ClusterSettings)
	if in.MergeType != nil {
		in, out := &in.MergeType, &out.MergeType
		*out = new(MergeType)
		**out = **in
	}
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimitSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.FaultInjection != nil {
		in, out := &in.FaultInjection, &out.FaultInjection
		*out = new(FaultInjection)
		(*in).DeepCopyInto(*out)
	}
	if in.UseClientProtocol != nil {
		in, out := &in.UseClientProtocol, &out.UseClientProtocol
		*out = new(bool)
		**out = **in
	}
	if in.Compression != nil {
		in, out := &in.Compression, &out.Compression
		*out = make([]*Compression, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(Compression)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.ResponseOverride != nil {
		in, out := &in.ResponseOverride, &out.ResponseOverride
		*out = make([]*ResponseOverride, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ResponseOverride)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.HTTPUpgrade != nil {
		in, out := &in.HTTPUpgrade, &out.HTTPUpgrade
		*out = make([]*ProtocolUpgradeConfig, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ProtocolUpgradeConfig)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.RequestBuffer != nil {
		in, out := &in.RequestBuffer, &out.RequestBuffer
		*out = new(RequestBuffer)
		(*in).DeepCopyInto(*out)
	}
	if in.Telemetry != nil {
		in, out := &in.Telemetry, &out.Telemetry
		*out = new(BackendTelemetry)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendTrafficPolicySpec.
func (in *BackendTrafficPolicySpec) DeepCopy() *BackendTrafficPolicySpec {
	if in == nil {
		return nil
	}
	out := new(BackendTrafficPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BasicAuth) DeepCopyInto(out *BasicAuth) {
	*out = *in
	in.Users.DeepCopyInto(&out.Users)
	if in.ForwardUsernameHeader != nil {
		in, out := &in.ForwardUsernameHeader, &out.ForwardUsernameHeader
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BasicAuth.
func (in *BasicAuth) DeepCopy() *BasicAuth {
	if in == nil {
		return nil
	}
	out := new(BasicAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BodyToExtAuth) DeepCopyInto(out *BodyToExtAuth) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BodyToExtAuth.
func (in *BodyToExtAuth) DeepCopy() *BodyToExtAuth {
	if in == nil {
		return nil
	}
	out := new(BodyToExtAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BrotliCompressor) DeepCopyInto(out *BrotliCompressor) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BrotliCompressor.
func (in *BrotliCompressor) DeepCopy() *BrotliCompressor {
	if in == nil {
		return nil
	}
	out := new(BrotliCompressor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CORS) DeepCopyInto(out *CORS) {
	*out = *in
	if in.AllowOrigins != nil {
		in, out := &in.AllowOrigins, &out.AllowOrigins
		*out = make([]Origin, len(*in))
		copy(*out, *in)
	}
	if in.AllowMethods != nil {
		in, out := &in.AllowMethods, &out.AllowMethods
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowHeaders != nil {
		in, out := &in.AllowHeaders, &out.AllowHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ExposeHeaders != nil {
		in, out := &in.ExposeHeaders, &out.ExposeHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MaxAge != nil {
		in, out := &in.MaxAge, &out.MaxAge
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.AllowCredentials != nil {
		in, out := &in.AllowCredentials, &out.AllowCredentials
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CORS.
func (in *CORS) DeepCopy() *CORS {
	if in == nil {
		return nil
	}
	out := new(CORS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CircuitBreaker) DeepCopyInto(out *CircuitBreaker) {
	*out = *in
	if in.MaxConnections != nil {
		in, out := &in.MaxConnections, &out.MaxConnections
		*out = new(int64)
		**out = **in
	}
	if in.MaxPendingRequests != nil {
		in, out := &in.MaxPendingRequests, &out.MaxPendingRequests
		*out = new(int64)
		**out = **in
	}
	if in.MaxParallelRequests != nil {
		in, out := &in.MaxParallelRequests, &out.MaxParallelRequests
		*out = new(int64)
		**out = **in
	}
	if in.MaxParallelRetries != nil {
		in, out := &in.MaxParallelRetries, &out.MaxParallelRetries
		*out = new(int64)
		**out = **in
	}
	if in.MaxRequestsPerConnection != nil {
		in, out := &in.MaxRequestsPerConnection, &out.MaxRequestsPerConnection
		*out = new(int64)
		**out = **in
	}
	if in.PerEndpoint != nil {
		in, out := &in.PerEndpoint, &out.PerEndpoint
		*out = new(PerEndpointCircuitBreakers)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CircuitBreaker.
func (in *CircuitBreaker) DeepCopy() *CircuitBreaker {
	if in == nil {
		return nil
	}
	out := new(CircuitBreaker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClaimToHeader) DeepCopyInto(out *ClaimToHeader) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClaimToHeader.
func (in *ClaimToHeader) DeepCopy() *ClaimToHeader {
	if in == nil {
		return nil
	}
	out := new(ClaimToHeader)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientConnection) DeepCopyInto(out *ClientConnection) {
	*out = *in
	if in.ConnectionLimit != nil {
		in, out := &in.ConnectionLimit, &out.ConnectionLimit
		*out = new(ConnectionLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.BufferLimit != nil {
		in, out := &in.BufferLimit, &out.BufferLimit
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.SocketBufferLimit != nil {
		in, out := &in.SocketBufferLimit, &out.SocketBufferLimit
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.MaxAcceptPerSocketEvent != nil {
		in, out := &in.MaxAcceptPerSocketEvent, &out.MaxAcceptPerSocketEvent
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientConnection.
func (in *ClientConnection) DeepCopy() *ClientConnection {
	if in == nil {
		return nil
	}
	out := new(ClientConnection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientIPDetectionSettings) DeepCopyInto(out *ClientIPDetectionSettings) {
	*out = *in
	if in.XForwardedFor != nil {
		in, out := &in.XForwardedFor, &out.XForwardedFor
		*out = new(XForwardedForSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomHeader != nil {
		in, out := &in.CustomHeader, &out.CustomHeader
		*out = new(CustomHeaderExtensionSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientIPDetectionSettings.
func (in *ClientIPDetectionSettings) DeepCopy() *ClientIPDetectionSettings {
	if in == nil {
		return nil
	}
	out := new(ClientIPDetectionSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTLSSettings) DeepCopyInto(out *ClientTLSSettings) {
	*out = *in
	if in.ClientValidation != nil {
		in, out := &in.ClientValidation, &out.ClientValidation
		*out = new(ClientValidationContext)
		(*in).DeepCopyInto(*out)
	}
	in.TLSSettings.DeepCopyInto(&out.TLSSettings)
	if in.Session != nil {
		in, out := &in.Session, &out.Session
		*out = new(Session)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTLSSettings.
func (in *ClientTLSSettings) DeepCopy() *ClientTLSSettings {
	if in == nil {
		return nil
	}
	out := new(ClientTLSSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTimeout) DeepCopyInto(out *ClientTimeout) {
	*out = *in
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPClientTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPClientTimeout)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTimeout.
func (in *ClientTimeout) DeepCopy() *ClientTimeout {
	if in == nil {
		return nil
	}
	out := new(ClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTrafficPolicy) DeepCopyInto(out *ClientTrafficPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTrafficPolicy.
func (in *ClientTrafficPolicy) DeepCopy() *ClientTrafficPolicy {
	if in == nil {
		return nil
	}
	out := new(ClientTrafficPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClientTrafficPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTrafficPolicyList) DeepCopyInto(out *ClientTrafficPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ClientTrafficPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTrafficPolicyList.
func (in *ClientTrafficPolicyList) DeepCopy() *ClientTrafficPolicyList {
	if in == nil {
		return nil
	}
	out := new(ClientTrafficPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClientTrafficPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTrafficPolicySpec) DeepCopyInto(out *ClientTrafficPolicySpec) {
	*out = *in
	in.PolicyTargetReferences.DeepCopyInto(&out.PolicyTargetReferences)
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.EnableProxyProtocol != nil {
		in, out := &in.EnableProxyProtocol, &out.EnableProxyProtocol
		*out = new(bool)
		**out = **in
	}
	if in.ClientIPDetection != nil {
		in, out := &in.ClientIPDetection, &out.ClientIPDetection
		*out = new(ClientIPDetectionSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(ClientTLSSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(PathSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = new(HeaderSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(ClientTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.Connection != nil {
		in, out := &in.Connection, &out.Connection
		*out = new(ClientConnection)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP1 != nil {
		in, out := &in.HTTP1, &out.HTTP1
		*out = new(HTTP1Settings)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP2 != nil {
		in, out := &in.HTTP2, &out.HTTP2
		*out = new(HTTP2Settings)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP3 != nil {
		in, out := &in.HTTP3, &out.HTTP3
		*out = new(HTTP3Settings)
		**out = **in
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheckSettings)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTrafficPolicySpec.
func (in *ClientTrafficPolicySpec) DeepCopy() *ClientTrafficPolicySpec {
	if in == nil {
		return nil
	}
	out := new(ClientTrafficPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientValidationContext) DeepCopyInto(out *ClientValidationContext) {
	*out = *in
	if in.CACertificateRefs != nil {
		in, out := &in.CACertificateRefs, &out.CACertificateRefs
		*out = make([]v1.SecretObjectReference, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientValidationContext.
func (in *ClientValidationContext) DeepCopy() *ClientValidationContext {
	if in == nil {
		return nil
	}
	out := new(ClientValidationContext)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterSettings) DeepCopyInto(out *ClusterSettings) {
	*out = *in
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(LoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.Retry != nil {
		in, out := &in.Retry, &out.Retry
		*out = new(Retry)
		(*in).DeepCopyInto(*out)
	}
	if in.ProxyProtocol != nil {
		in, out := &in.ProxyProtocol, &out.ProxyProtocol
		*out = new(ProxyProtocol)
		**out = **in
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.CircuitBreaker != nil {
		in, out := &in.CircuitBreaker, &out.CircuitBreaker
		*out = new(CircuitBreaker)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(Timeout)
		(*in).DeepCopyInto(*out)
	}
	if in.Connection != nil {
		in, out := &in.Connection, &out.Connection
		*out = new(BackendConnection)
		(*in).DeepCopyInto(*out)
	}
	if in.DNS != nil {
		in, out := &in.DNS, &out.DNS
		*out = new(DNS)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP2 != nil {
		in, out := &in.HTTP2, &out.HTTP2
		*out = new(HTTP2Settings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterSettings.
func (in *ClusterSettings) DeepCopy() *ClusterSettings {
	if in == nil {
		return nil
	}
	out := new(ClusterSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Compression) DeepCopyInto(out *Compression) {
	*out = *in
	if in.Brotli != nil {
		in, out := &in.Brotli, &out.Brotli
		*out = new(BrotliCompressor)
		**out = **in
	}
	if in.Gzip != nil {
		in, out := &in.Gzip, &out.Gzip
		*out = new(GzipCompressor)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Compression.
func (in *Compression) DeepCopy() *Compression {
	if in == nil {
		return nil
	}
	out := new(Compression)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConnectConfig) DeepCopyInto(out *ConnectConfig) {
	*out = *in
	if in.Terminate != nil {
		in, out := &in.Terminate, &out.Terminate
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConnectConfig.
func (in *ConnectConfig) DeepCopy() *ConnectConfig {
	if in == nil {
		return nil
	}
	out := new(ConnectConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConnectionLimit) DeepCopyInto(out *ConnectionLimit) {
	*out = *in
	if in.CloseDelay != nil {
		in, out := &in.CloseDelay, &out.CloseDelay
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConnectionLimit.
func (in *ConnectionLimit) DeepCopy() *ConnectionLimit {
	if in == nil {
		return nil
	}
	out := new(ConnectionLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConsistentHash) DeepCopyInto(out *ConsistentHash) {
	*out = *in
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(Header)
		**out = **in
	}
	if in.Cookie != nil {
		in, out := &in.Cookie, &out.Cookie
		*out = new(Cookie)
		(*in).DeepCopyInto(*out)
	}
	if in.TableSize != nil {
		in, out := &in.TableSize, &out.TableSize
		*out = new(uint64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConsistentHash.
func (in *ConsistentHash) DeepCopy() *ConsistentHash {
	if in == nil {
		return nil
	}
	out := new(ConsistentHash)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Cookie) DeepCopyInto(out *Cookie) {
	*out = *in
	if in.TTL != nil {
		in, out := &in.TTL, &out.TTL
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.Attributes != nil {
		in, out := &in.Attributes, &out.Attributes
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Cookie.
func (in *Cookie) DeepCopy() *Cookie {
	if in == nil {
		return nil
	}
	out := new(Cookie)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomHeaderExtensionSettings) DeepCopyInto(out *CustomHeaderExtensionSettings) {
	*out = *in
	if in.FailClosed != nil {
		in, out := &in.FailClosed, &out.FailClosed
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomHeaderExtensionSettings.
func (in *CustomHeaderExtensionSettings) DeepCopy() *CustomHeaderExtensionSettings {
	if in == nil {
		return nil
	}
	out := new(CustomHeaderExtensionSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomRedirect) DeepCopyInto(out *CustomRedirect) {
	*out = *in
	if in.Scheme != nil {
		in, out := &in.Scheme, &out.Scheme
		*out = new(string)
		**out = **in
	}
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(v1.PreciseHostname)
		**out = **in
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(v1.HTTPPathModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.Port != nil {
		in, out := &in.Port, &out.Port
		*out = new(v1.PortNumber)
		**out = **in
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(int)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomRedirect.
func (in *CustomRedirect) DeepCopy() *CustomRedirect {
	if in == nil {
		return nil
	}
	out := new(CustomRedirect)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponse) DeepCopyInto(out *CustomResponse) {
	*out = *in
	if in.ContentType != nil {
		in, out := &in.ContentType, &out.ContentType
		*out = new(string)
		**out = **in
	}
	if in.Body != nil {
		in, out := &in.Body, &out.Body
		*out = new(CustomResponseBody)
		(*in).DeepCopyInto(*out)
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(int)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponse.
func (in *CustomResponse) DeepCopy() *CustomResponse {
	if in == nil {
		return nil
	}
	out := new(CustomResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponseBody) DeepCopyInto(out *CustomResponseBody) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(ResponseValueType)
		**out = **in
	}
	if in.Inline != nil {
		in, out := &in.Inline, &out.Inline
		*out = new(string)
		**out = **in
	}
	if in.ValueRef != nil {
		in, out := &in.ValueRef, &out.ValueRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponseBody.
func (in *CustomResponseBody) DeepCopy() *CustomResponseBody {
	if in == nil {
		return nil
	}
	out := new(CustomResponseBody)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponseMatch) DeepCopyInto(out *CustomResponseMatch) {
	*out = *in
	if in.StatusCodes != nil {
		in, out := &in.StatusCodes, &out.StatusCodes
		*out = make([]StatusCodeMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponseMatch.
func (in *CustomResponseMatch) DeepCopy() *CustomResponseMatch {
	if in == nil {
		return nil
	}
	out := new(CustomResponseMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomTag) DeepCopyInto(out *CustomTag) {
	*out = *in
	if in.Literal != nil {
		in, out := &in.Literal, &out.Literal
		*out = new(LiteralCustomTag)
		**out = **in
	}
	if in.Environment != nil {
		in, out := &in.Environment, &out.Environment
		*out = new(EnvironmentCustomTag)
		(*in).DeepCopyInto(*out)
	}
	if in.RequestHeader != nil {
		in, out := &in.RequestHeader, &out.RequestHeader
		*out = new(RequestHeaderCustomTag)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomTag.
func (in *CustomTag) DeepCopy() *CustomTag {
	if in == nil {
		return nil
	}
	out := new(CustomTag)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DNS) DeepCopyInto(out *DNS) {
	*out = *in
	if in.DNSRefreshRate != nil {
		in, out := &in.DNSRefreshRate, &out.DNSRefreshRate
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.RespectDNSTTL != nil {
		in, out := &in.RespectDNSTTL, &out.RespectDNSTTL
		*out = new(bool)
		**out = **in
	}
	if in.LookupFamily != nil {
		in, out := &in.LookupFamily, &out.LookupFamily
		*out = new(DNSLookupFamily)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DNS.
func (in *DNS) DeepCopy() *DNS {
	if in == nil {
		return nil
	}
	out := new(DNS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvironmentCustomTag) DeepCopyInto(out *EnvironmentCustomTag) {
	*out = *in
	if in.DefaultValue != nil {
		in, out := &in.DefaultValue, &out.DefaultValue
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvironmentCustomTag.
func (in *EnvironmentCustomTag) DeepCopy() *EnvironmentCustomTag {
	if in == nil {
		return nil
	}
	out := new(EnvironmentCustomTag)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyExtensionPolicy) DeepCopyInto(out *EnvoyExtensionPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyExtensionPolicy.
func (in *EnvoyExtensionPolicy) DeepCopy() *EnvoyExtensionPolicy {
	if in == nil {
		return nil
	}
	out := new(EnvoyExtensionPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyExtensionPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyExtensionPolicyList) DeepCopyInto(out *EnvoyExtensionPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]EnvoyExtensionPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyExtensionPolicyList.
func (in *EnvoyExtensionPolicyList) DeepCopy() *EnvoyExtensionPolicyList {
	if in == nil {
		return nil
	}
	out := new(EnvoyExtensionPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyExtensionPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyExtensionPolicySpec) DeepCopyInto(out *EnvoyExtensionPolicySpec) {
	*out = *in
	in.PolicyTargetReferences.DeepCopyInto(&out.PolicyTargetReferences)
	if in.Wasm != nil {
		in, out := &in.Wasm, &out.Wasm
		*out = make([]Wasm, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ExtProc != nil {
		in, out := &in.ExtProc, &out.ExtProc
		*out = make([]ExtProc, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Lua != nil {
		in, out := &in.Lua, &out.Lua
		*out = make([]Lua, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyExtensionPolicySpec.
func (in *EnvoyExtensionPolicySpec) DeepCopy() *EnvoyExtensionPolicySpec {
	if in == nil {
		return nil
	}
	out := new(EnvoyExtensionPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGateway) DeepCopyInto(out *EnvoyGateway) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.EnvoyGatewaySpec.DeepCopyInto(&out.EnvoyGatewaySpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGateway.
func (in *EnvoyGateway) DeepCopy() *EnvoyGateway {
	if in == nil {
		return nil
	}
	out := new(EnvoyGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyGateway) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayAdmin) DeepCopyInto(out *EnvoyGatewayAdmin) {
	*out = *in
	if in.Address != nil {
		in, out := &in.Address, &out.Address
		*out = new(EnvoyGatewayAdminAddress)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayAdmin.
func (in *EnvoyGatewayAdmin) DeepCopy() *EnvoyGatewayAdmin {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayAdmin)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayAdminAddress) DeepCopyInto(out *EnvoyGatewayAdminAddress) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayAdminAddress.
func (in *EnvoyGatewayAdminAddress) DeepCopy() *EnvoyGatewayAdminAddress {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayAdminAddress)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayCustomProvider) DeepCopyInto(out *EnvoyGatewayCustomProvider) {
	*out = *in
	in.Resource.DeepCopyInto(&out.Resource)
	if in.Infrastructure != nil {
		in, out := &in.Infrastructure, &out.Infrastructure
		*out = new(EnvoyGatewayInfrastructureProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayCustomProvider.
func (in *EnvoyGatewayCustomProvider) DeepCopy() *EnvoyGatewayCustomProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayCustomProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayFileResourceProvider) DeepCopyInto(out *EnvoyGatewayFileResourceProvider) {
	*out = *in
	if in.Paths != nil {
		in, out := &in.Paths, &out.Paths
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayFileResourceProvider.
func (in *EnvoyGatewayFileResourceProvider) DeepCopy() *EnvoyGatewayFileResourceProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayFileResourceProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayHostInfrastructureProvider) DeepCopyInto(out *EnvoyGatewayHostInfrastructureProvider) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayHostInfrastructureProvider.
func (in *EnvoyGatewayHostInfrastructureProvider) DeepCopy() *EnvoyGatewayHostInfrastructureProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayHostInfrastructureProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayInfrastructureProvider) DeepCopyInto(out *EnvoyGatewayInfrastructureProvider) {
	*out = *in
	if in.Host != nil {
		in, out := &in.Host, &out.Host
		*out = new(EnvoyGatewayHostInfrastructureProvider)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayInfrastructureProvider.
func (in *EnvoyGatewayInfrastructureProvider) DeepCopy() *EnvoyGatewayInfrastructureProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayInfrastructureProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayKubernetesProvider) DeepCopyInto(out *EnvoyGatewayKubernetesProvider) {
	*out = *in
	if in.RateLimitDeployment != nil {
		in, out := &in.RateLimitDeployment, &out.RateLimitDeployment
		*out = new(KubernetesDeploymentSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.RateLimitHpa != nil {
		in, out := &in.RateLimitHpa, &out.RateLimitHpa
		*out = new(KubernetesHorizontalPodAutoscalerSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Watch != nil {
		in, out := &in.Watch, &out.Watch
		*out = new(KubernetesWatchMode)
		(*in).DeepCopyInto(*out)
	}
	if in.Deploy != nil {
		in, out := &in.Deploy, &out.Deploy
		*out = new(KubernetesDeployMode)
		(*in).DeepCopyInto(*out)
	}
	if in.LeaderElection != nil {
		in, out := &in.LeaderElection, &out.LeaderElection
		*out = new(LeaderElection)
		(*in).DeepCopyInto(*out)
	}
	if in.ShutdownManager != nil {
		in, out := &in.ShutdownManager, &out.ShutdownManager
		*out = new(ShutdownManager)
		(*in).DeepCopyInto(*out)
	}
	if in.Client != nil {
		in, out := &in.Client, &out.Client
		*out = new(KubernetesClient)
		(*in).DeepCopyInto(*out)
	}
	if in.TopologyInjector != nil {
		in, out := &in.TopologyInjector, &out.TopologyInjector
		*out = new(EnvoyGatewayTopologyInjector)
		(*in).DeepCopyInto(*out)
	}
	if in.CacheSyncPeriod != nil {
		in, out := &in.CacheSyncPeriod, &out.CacheSyncPeriod
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayKubernetesProvider.
func (in *EnvoyGatewayKubernetesProvider) DeepCopy() *EnvoyGatewayKubernetesProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayKubernetesProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayLogging) DeepCopyInto(out *EnvoyGatewayLogging) {
	*out = *in
	if in.Level != nil {
		in, out := &in.Level, &out.Level
		*out = make(map[EnvoyGatewayLogComponent]LogLevel, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayLogging.
func (in *EnvoyGatewayLogging) DeepCopy() *EnvoyGatewayLogging {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayLogging)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayMetricSink) DeepCopyInto(out *EnvoyGatewayMetricSink) {
	*out = *in
	if in.OpenTelemetry != nil {
		in, out := &in.OpenTelemetry, &out.OpenTelemetry
		*out = new(EnvoyGatewayOpenTelemetrySink)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayMetricSink.
func (in *EnvoyGatewayMetricSink) DeepCopy() *EnvoyGatewayMetricSink {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayMetricSink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayMetrics) DeepCopyInto(out *EnvoyGatewayMetrics) {
	*out = *in
	if in.Sinks != nil {
		in, out := &in.Sinks, &out.Sinks
		*out = make([]EnvoyGatewayMetricSink, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Prometheus != nil {
		in, out := &in.Prometheus, &out.Prometheus
		*out = new(EnvoyGatewayPrometheusProvider)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayMetrics.
func (in *EnvoyGatewayMetrics) DeepCopy() *EnvoyGatewayMetrics {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayMetrics)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayOpenTelemetrySink) DeepCopyInto(out *EnvoyGatewayOpenTelemetrySink) {
	*out = *in
	if in.ExportInterval != nil {
		in, out := &in.ExportInterval, &out.ExportInterval
		*out = new(v1.Duration)
		**out = **in
	}
	if in.ExportTimeout != nil {
		in, out := &in.ExportTimeout, &out.ExportTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayOpenTelemetrySink.
func (in *EnvoyGatewayOpenTelemetrySink) DeepCopy() *EnvoyGatewayOpenTelemetrySink {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayOpenTelemetrySink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayPrometheusProvider) DeepCopyInto(out *EnvoyGatewayPrometheusProvider) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayPrometheusProvider.
func (in *EnvoyGatewayPrometheusProvider) DeepCopy() *EnvoyGatewayPrometheusProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayPrometheusProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayProvider) DeepCopyInto(out *EnvoyGatewayProvider) {
	*out = *in
	if in.Kubernetes != nil {
		in, out := &in.Kubernetes, &out.Kubernetes
		*out = new(EnvoyGatewayKubernetesProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.Custom != nil {
		in, out := &in.Custom, &out.Custom
		*out = new(EnvoyGatewayCustomProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayProvider.
func (in *EnvoyGatewayProvider) DeepCopy() *EnvoyGatewayProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayResourceProvider) DeepCopyInto(out *EnvoyGatewayResourceProvider) {
	*out = *in
	if in.File != nil {
		in, out := &in.File, &out.File
		*out = new(EnvoyGatewayFileResourceProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayResourceProvider.
func (in *EnvoyGatewayResourceProvider) DeepCopy() *EnvoyGatewayResourceProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayResourceProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewaySpec) DeepCopyInto(out *EnvoyGatewaySpec) {
	*out = *in
	if in.Gateway != nil {
		in, out := &in.Gateway, &out.Gateway
		*out = new(Gateway)
		**out = **in
	}
	if in.Provider != nil {
		in, out := &in.Provider, &out.Provider
		*out = new(EnvoyGatewayProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.Logging != nil {
		in, out := &in.Logging, &out.Logging
		*out = new(EnvoyGatewayLogging)
		(*in).DeepCopyInto(*out)
	}
	if in.Admin != nil {
		in, out := &in.Admin, &out.Admin
		*out = new(EnvoyGatewayAdmin)
		(*in).DeepCopyInto(*out)
	}
	if in.Telemetry != nil {
		in, out := &in.Telemetry, &out.Telemetry
		*out = new(EnvoyGatewayTelemetry)
		(*in).DeepCopyInto(*out)
	}
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtensionManager != nil {
		in, out := &in.ExtensionManager, &out.ExtensionManager
		*out = new(ExtensionManager)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtensionAPIs != nil {
		in, out := &in.ExtensionAPIs, &out.ExtensionAPIs
		*out = new(ExtensionAPISettings)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewaySpec.
func (in *EnvoyGatewaySpec) DeepCopy() *EnvoyGatewaySpec {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewaySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayTelemetry) DeepCopyInto(out *EnvoyGatewayTelemetry) {
	*out = *in
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = new(EnvoyGatewayMetrics)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayTelemetry.
func (in *EnvoyGatewayTelemetry) DeepCopy() *EnvoyGatewayTelemetry {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayTelemetry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyGatewayTopologyInjector) DeepCopyInto(out *EnvoyGatewayTopologyInjector) {
	*out = *in
	if in.Disable != nil {
		in, out := &in.Disable, &out.Disable
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyGatewayTopologyInjector.
func (in *EnvoyGatewayTopologyInjector) DeepCopy() *EnvoyGatewayTopologyInjector {
	if in == nil {
		return nil
	}
	out := new(EnvoyGatewayTopologyInjector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyJSONPatchConfig) DeepCopyInto(out *EnvoyJSONPatchConfig) {
	*out = *in
	in.Operation.DeepCopyInto(&out.Operation)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyJSONPatchConfig.
func (in *EnvoyJSONPatchConfig) DeepCopy() *EnvoyJSONPatchConfig {
	if in == nil {
		return nil
	}
	out := new(EnvoyJSONPatchConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyPatchPolicy) DeepCopyInto(out *EnvoyPatchPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyPatchPolicy.
func (in *EnvoyPatchPolicy) DeepCopy() *EnvoyPatchPolicy {
	if in == nil {
		return nil
	}
	out := new(EnvoyPatchPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyPatchPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyPatchPolicyList) DeepCopyInto(out *EnvoyPatchPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]EnvoyPatchPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyPatchPolicyList.
func (in *EnvoyPatchPolicyList) DeepCopy() *EnvoyPatchPolicyList {
	if in == nil {
		return nil
	}
	out := new(EnvoyPatchPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyPatchPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyPatchPolicySpec) DeepCopyInto(out *EnvoyPatchPolicySpec) {
	*out = *in
	if in.JSONPatches != nil {
		in, out := &in.JSONPatches, &out.JSONPatches
		*out = make([]EnvoyJSONPatchConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	in.TargetRef.DeepCopyInto(&out.TargetRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyPatchPolicySpec.
func (in *EnvoyPatchPolicySpec) DeepCopy() *EnvoyPatchPolicySpec {
	if in == nil {
		return nil
	}
	out := new(EnvoyPatchPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxy) DeepCopyInto(out *EnvoyProxy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxy.
func (in *EnvoyProxy) DeepCopy() *EnvoyProxy {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyProxy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxyKubernetesProvider) DeepCopyInto(out *EnvoyProxyKubernetesProvider) {
	*out = *in
	if in.EnvoyDeployment != nil {
		in, out := &in.EnvoyDeployment, &out.EnvoyDeployment
		*out = new(KubernetesDeploymentSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyDaemonSet != nil {
		in, out := &in.EnvoyDaemonSet, &out.EnvoyDaemonSet
		*out = new(KubernetesDaemonSetSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyService != nil {
		in, out := &in.EnvoyService, &out.EnvoyService
		*out = new(KubernetesServiceSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyHpa != nil {
		in, out := &in.EnvoyHpa, &out.EnvoyHpa
		*out = new(KubernetesHorizontalPodAutoscalerSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.UseListenerPortAsContainerPort != nil {
		in, out := &in.UseListenerPortAsContainerPort, &out.UseListenerPortAsContainerPort
		*out = new(bool)
		**out = **in
	}
	if in.EnvoyPDB != nil {
		in, out := &in.EnvoyPDB, &out.EnvoyPDB
		*out = new(KubernetesPodDisruptionBudgetSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyServiceAccount != nil {
		in, out := &in.EnvoyServiceAccount, &out.EnvoyServiceAccount
		*out = new(KubernetesServiceAccountSpec)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxyKubernetesProvider.
func (in *EnvoyProxyKubernetesProvider) DeepCopy() *EnvoyProxyKubernetesProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxyKubernetesProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxyList) DeepCopyInto(out *EnvoyProxyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]EnvoyProxy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxyList.
func (in *EnvoyProxyList) DeepCopy() *EnvoyProxyList {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EnvoyProxyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxyProvider) DeepCopyInto(out *EnvoyProxyProvider) {
	*out = *in
	if in.Kubernetes != nil {
		in, out := &in.Kubernetes, &out.Kubernetes
		*out = new(EnvoyProxyKubernetesProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxyProvider.
func (in *EnvoyProxyProvider) DeepCopy() *EnvoyProxyProvider {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxyProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxySpec) DeepCopyInto(out *EnvoyProxySpec) {
	*out = *in
	if in.Provider != nil {
		in, out := &in.Provider, &out.Provider
		*out = new(EnvoyProxyProvider)
		(*in).DeepCopyInto(*out)
	}
	in.Logging.DeepCopyInto(&out.Logging)
	if in.Telemetry != nil {
		in, out := &in.Telemetry, &out.Telemetry
		*out = new(ProxyTelemetry)
		(*in).DeepCopyInto(*out)
	}
	if in.Bootstrap != nil {
		in, out := &in.Bootstrap, &out.Bootstrap
		*out = new(ProxyBootstrap)
		(*in).DeepCopyInto(*out)
	}
	if in.Concurrency != nil {
		in, out := &in.Concurrency, &out.Concurrency
		*out = new(int32)
		**out = **in
	}
	if in.RoutingType != nil {
		in, out := &in.RoutingType, &out.RoutingType
		*out = new(RoutingType)
		**out = **in
	}
	if in.ExtraArgs != nil {
		in, out := &in.ExtraArgs, &out.ExtraArgs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MergeGateways != nil {
		in, out := &in.MergeGateways, &out.MergeGateways
		*out = new(bool)
		**out = **in
	}
	if in.Shutdown != nil {
		in, out := &in.Shutdown, &out.Shutdown
		*out = new(ShutdownConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.FilterOrder != nil {
		in, out := &in.FilterOrder, &out.FilterOrder
		*out = make([]FilterPosition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.BackendTLS != nil {
		in, out := &in.BackendTLS, &out.BackendTLS
		*out = new(BackendTLSConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.IPFamily != nil {
		in, out := &in.IPFamily, &out.IPFamily
		*out = new(IPFamily)
		**out = **in
	}
	if in.PreserveRouteOrder != nil {
		in, out := &in.PreserveRouteOrder, &out.PreserveRouteOrder
		*out = new(bool)
		**out = **in
	}
	if in.DisableLuaValidation != nil {
		in, out := &in.DisableLuaValidation, &out.DisableLuaValidation
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxySpec.
func (in *EnvoyProxySpec) DeepCopy() *EnvoyProxySpec {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyProxyStatus) DeepCopyInto(out *EnvoyProxyStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyProxyStatus.
func (in *EnvoyProxyStatus) DeepCopy() *EnvoyProxyStatus {
	if in == nil {
		return nil
	}
	out := new(EnvoyProxyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtAuth) DeepCopyInto(out *ExtAuth) {
	*out = *in
	if in.GRPC != nil {
		in, out := &in.GRPC, &out.GRPC
		*out = new(GRPCExtAuthService)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPExtAuthService)
		(*in).DeepCopyInto(*out)
	}
	if in.HeadersToExtAuth != nil {
		in, out := &in.HeadersToExtAuth, &out.HeadersToExtAuth
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.BodyToExtAuth != nil {
		in, out := &in.BodyToExtAuth, &out.BodyToExtAuth
		*out = new(BodyToExtAuth)
		**out = **in
	}
	if in.FailOpen != nil {
		in, out := &in.FailOpen, &out.FailOpen
		*out = new(bool)
		**out = **in
	}
	if in.RecomputeRoute != nil {
		in, out := &in.RecomputeRoute, &out.RecomputeRoute
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtAuth.
func (in *ExtAuth) DeepCopy() *ExtAuth {
	if in == nil {
		return nil
	}
	out := new(ExtAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProc) DeepCopyInto(out *ExtProc) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.MessageTimeout != nil {
		in, out := &in.MessageTimeout, &out.MessageTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.FailOpen != nil {
		in, out := &in.FailOpen, &out.FailOpen
		*out = new(bool)
		**out = **in
	}
	if in.ProcessingMode != nil {
		in, out := &in.ProcessingMode, &out.ProcessingMode
		*out = new(ExtProcProcessingMode)
		(*in).DeepCopyInto(*out)
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(ExtProcMetadata)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProc.
func (in *ExtProc) DeepCopy() *ExtProc {
	if in == nil {
		return nil
	}
	out := new(ExtProc)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProcMetadata) DeepCopyInto(out *ExtProcMetadata) {
	*out = *in
	if in.AccessibleNamespaces != nil {
		in, out := &in.AccessibleNamespaces, &out.AccessibleNamespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.WritableNamespaces != nil {
		in, out := &in.WritableNamespaces, &out.WritableNamespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProcMetadata.
func (in *ExtProcMetadata) DeepCopy() *ExtProcMetadata {
	if in == nil {
		return nil
	}
	out := new(ExtProcMetadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProcProcessingMode) DeepCopyInto(out *ExtProcProcessingMode) {
	*out = *in
	if in.Request != nil {
		in, out := &in.Request, &out.Request
		*out = new(ProcessingModeOptions)
		(*in).DeepCopyInto(*out)
	}
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(ProcessingModeOptions)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProcProcessingMode.
func (in *ExtProcProcessingMode) DeepCopy() *ExtProcProcessingMode {
	if in == nil {
		return nil
	}
	out := new(ExtProcProcessingMode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionAPISettings) DeepCopyInto(out *ExtensionAPISettings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionAPISettings.
func (in *ExtensionAPISettings) DeepCopy() *ExtensionAPISettings {
	if in == nil {
		return nil
	}
	out := new(ExtensionAPISettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionHooks) DeepCopyInto(out *ExtensionHooks) {
	*out = *in
	if in.XDSTranslator != nil {
		in, out := &in.XDSTranslator, &out.XDSTranslator
		*out = new(XDSTranslatorHooks)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionHooks.
func (in *ExtensionHooks) DeepCopy() *ExtensionHooks {
	if in == nil {
		return nil
	}
	out := new(ExtensionHooks)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionManager) DeepCopyInto(out *ExtensionManager) {
	*out = *in
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]GroupVersionKind, len(*in))
		copy(*out, *in)
	}
	if in.PolicyResources != nil {
		in, out := &in.PolicyResources, &out.PolicyResources
		*out = make([]GroupVersionKind, len(*in))
		copy(*out, *in)
	}
	if in.Hooks != nil {
		in, out := &in.Hooks, &out.Hooks
		*out = new(ExtensionHooks)
		(*in).DeepCopyInto(*out)
	}
	if in.Service != nil {
		in, out := &in.Service, &out.Service
		*out = new(ExtensionService)
		(*in).DeepCopyInto(*out)
	}
	if in.MaxMessageSize != nil {
		in, out := &in.MaxMessageSize, &out.MaxMessageSize
		x := (*in).DeepCopy()
		*out = &x
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionManager.
func (in *ExtensionManager) DeepCopy() *ExtensionManager {
	if in == nil {
		return nil
	}
	out := new(ExtensionManager)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionService) DeepCopyInto(out *ExtensionService) {
	*out = *in
	in.BackendEndpoint.DeepCopyInto(&out.BackendEndpoint)
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(ExtensionTLS)
		(*in).DeepCopyInto(*out)
	}
	if in.Retry != nil {
		in, out := &in.Retry, &out.Retry
		*out = new(ExtensionServiceRetry)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionService.
func (in *ExtensionService) DeepCopy() *ExtensionService {
	if in == nil {
		return nil
	}
	out := new(ExtensionService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionServiceRetry) DeepCopyInto(out *ExtensionServiceRetry) {
	*out = *in
	if in.MaxAttempts != nil {
		in, out := &in.MaxAttempts, &out.MaxAttempts
		*out = new(int)
		**out = **in
	}
	if in.InitialBackoff != nil {
		in, out := &in.InitialBackoff, &out.InitialBackoff
		*out = new(v1.Duration)
		**out = **in
	}
	if in.MaxBackoff != nil {
		in, out := &in.MaxBackoff, &out.MaxBackoff
		*out = new(v1.Duration)
		**out = **in
	}
	if in.BackoffMultiplier != nil {
		in, out := &in.BackoffMultiplier, &out.BackoffMultiplier
		*out = new(v1.Fraction)
		(*in).DeepCopyInto(*out)
	}
	if in.RetryableStatusCodes != nil {
		in, out := &in.RetryableStatusCodes, &out.RetryableStatusCodes
		*out = make([]RetryableGRPCStatusCode, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionServiceRetry.
func (in *ExtensionServiceRetry) DeepCopy() *ExtensionServiceRetry {
	if in == nil {
		return nil
	}
	out := new(ExtensionServiceRetry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtensionTLS) DeepCopyInto(out *ExtensionTLS) {
	*out = *in
	in.CertificateRef.DeepCopyInto(&out.CertificateRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtensionTLS.
func (in *ExtensionTLS) DeepCopy() *ExtensionTLS {
	if in == nil {
		return nil
	}
	out := new(ExtensionTLS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtractFrom) DeepCopyInto(out *ExtractFrom) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Cookies != nil {
		in, out := &in.Cookies, &out.Cookies
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtractFrom.
func (in *ExtractFrom) DeepCopy() *ExtractFrom {
	if in == nil {
		return nil
	}
	out := new(ExtractFrom)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FQDNEndpoint) DeepCopyInto(out *FQDNEndpoint) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FQDNEndpoint.
func (in *FQDNEndpoint) DeepCopy() *FQDNEndpoint {
	if in == nil {
		return nil
	}
	out := new(FQDNEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjection) DeepCopyInto(out *FaultInjection) {
	*out = *in
	if in.Delay != nil {
		in, out := &in.Delay, &out.Delay
		*out = new(FaultInjectionDelay)
		(*in).DeepCopyInto(*out)
	}
	if in.Abort != nil {
		in, out := &in.Abort, &out.Abort
		*out = new(FaultInjectionAbort)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjection.
func (in *FaultInjection) DeepCopy() *FaultInjection {
	if in == nil {
		return nil
	}
	out := new(FaultInjection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjectionAbort) DeepCopyInto(out *FaultInjectionAbort) {
	*out = *in
	if in.HTTPStatus != nil {
		in, out := &in.HTTPStatus, &out.HTTPStatus
		*out = new(int32)
		**out = **in
	}
	if in.GrpcStatus != nil {
		in, out := &in.GrpcStatus, &out.GrpcStatus
		*out = new(int32)
		**out = **in
	}
	if in.Percentage != nil {
		in, out := &in.Percentage, &out.Percentage
		*out = new(float32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjectionAbort.
func (in *FaultInjectionAbort) DeepCopy() *FaultInjectionAbort {
	if in == nil {
		return nil
	}
	out := new(FaultInjectionAbort)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjectionDelay) DeepCopyInto(out *FaultInjectionDelay) {
	*out = *in
	if in.FixedDelay != nil {
		in, out := &in.FixedDelay, &out.FixedDelay
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.Percentage != nil {
		in, out := &in.Percentage, &out.Percentage
		*out = new(float32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjectionDelay.
func (in *FaultInjectionDelay) DeepCopy() *FaultInjectionDelay {
	if in == nil {
		return nil
	}
	out := new(FaultInjectionDelay)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileEnvoyProxyAccessLog) DeepCopyInto(out *FileEnvoyProxyAccessLog) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileEnvoyProxyAccessLog.
func (in *FileEnvoyProxyAccessLog) DeepCopy() *FileEnvoyProxyAccessLog {
	if in == nil {
		return nil
	}
	out := new(FileEnvoyProxyAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FilterPosition) DeepCopyInto(out *FilterPosition) {
	*out = *in
	if in.Before != nil {
		in, out := &in.Before, &out.Before
		*out = new(EnvoyFilter)
		**out = **in
	}
	if in.After != nil {
		in, out := &in.After, &out.After
		*out = new(EnvoyFilter)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FilterPosition.
func (in *FilterPosition) DeepCopy() *FilterPosition {
	if in == nil {
		return nil
	}
	out := new(FilterPosition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ForceLocalZone) DeepCopyInto(out *ForceLocalZone) {
	*out = *in
	if in.MinEndpointsInZoneThreshold != nil {
		in, out := &in.MinEndpointsInZoneThreshold, &out.MinEndpointsInZoneThreshold
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ForceLocalZone.
func (in *ForceLocalZone) DeepCopy() *ForceLocalZone {
	if in == nil {
		return nil
	}
	out := new(ForceLocalZone)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GRPCActiveHealthChecker) DeepCopyInto(out *GRPCActiveHealthChecker) {
	*out = *in
	if in.Service != nil {
		in, out := &in.Service, &out.Service
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GRPCActiveHealthChecker.
func (in *GRPCActiveHealthChecker) DeepCopy() *GRPCActiveHealthChecker {
	if in == nil {
		return nil
	}
	out := new(GRPCActiveHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GRPCExtAuthService) DeepCopyInto(out *GRPCExtAuthService) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GRPCExtAuthService.
func (in *GRPCExtAuthService) DeepCopy() *GRPCExtAuthService {
	if in == nil {
		return nil
	}
	out := new(GRPCExtAuthService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Gateway) DeepCopyInto(out *Gateway) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Gateway.
func (in *Gateway) DeepCopy() *Gateway {
	if in == nil {
		return nil
	}
	out := new(Gateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GlobalRateLimit) DeepCopyInto(out *GlobalRateLimit) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]RateLimitRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GlobalRateLimit.
func (in *GlobalRateLimit) DeepCopy() *GlobalRateLimit {
	if in == nil {
		return nil
	}
	out := new(GlobalRateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GroupVersionKind) DeepCopyInto(out *GroupVersionKind) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GroupVersionKind.
func (in *GroupVersionKind) DeepCopy() *GroupVersionKind {
	if in == nil {
		return nil
	}
	out := new(GroupVersionKind)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GzipCompressor) DeepCopyInto(out *GzipCompressor) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GzipCompressor.
func (in *GzipCompressor) DeepCopy() *GzipCompressor {
	if in == nil {
		return nil
	}
	out := new(GzipCompressor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP10Settings) DeepCopyInto(out *HTTP10Settings) {
	*out = *in
	if in.UseDefaultHost != nil {
		in, out := &in.UseDefaultHost, &out.UseDefaultHost
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP10Settings.
func (in *HTTP10Settings) DeepCopy() *HTTP10Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP10Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP1Settings) DeepCopyInto(out *HTTP1Settings) {
	*out = *in
	if in.EnableTrailers != nil {
		in, out := &in.EnableTrailers, &out.EnableTrailers
		*out = new(bool)
		**out = **in
	}
	if in.PreserveHeaderCase != nil {
		in, out := &in.PreserveHeaderCase, &out.PreserveHeaderCase
		*out = new(bool)
		**out = **in
	}
	if in.HTTP10 != nil {
		in, out := &in.HTTP10, &out.HTTP10
		*out = new(HTTP10Settings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP1Settings.
func (in *HTTP1Settings) DeepCopy() *HTTP1Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP1Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP2Settings) DeepCopyInto(out *HTTP2Settings) {
	*out = *in
	if in.InitialStreamWindowSize != nil {
		in, out := &in.InitialStreamWindowSize, &out.InitialStreamWindowSize
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.InitialConnectionWindowSize != nil {
		in, out := &in.InitialConnectionWindowSize, &out.InitialConnectionWindowSize
		x := (*in).DeepCopy()
		*out = &x
	}
	if in.MaxConcurrentStreams != nil {
		in, out := &in.MaxConcurrentStreams, &out.MaxConcurrentStreams
		*out = new(uint32)
		**out = **in
	}
	if in.OnInvalidMessage != nil {
		in, out := &in.OnInvalidMessage, &out.OnInvalidMessage
		*out = new(InvalidMessageAction)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP2Settings.
func (in *HTTP2Settings) DeepCopy() *HTTP2Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP2Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP3Settings) DeepCopyInto(out *HTTP3Settings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP3Settings.
func (in *HTTP3Settings) DeepCopy() *HTTP3Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP3Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPActiveHealthChecker) DeepCopyInto(out *HTTPActiveHealthChecker) {
	*out = *in
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(string)
		**out = **in
	}
	if in.Method != nil {
		in, out := &in.Method, &out.Method
		*out = new(string)
		**out = **in
	}
	if in.ExpectedStatuses != nil {
		in, out := &in.ExpectedStatuses, &out.ExpectedStatuses
		*out = make([]HTTPStatus, len(*in))
		copy(*out, *in)
	}
	if in.ExpectedResponse != nil {
		in, out := &in.ExpectedResponse, &out.ExpectedResponse
		*out = new(ActiveHealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPActiveHealthChecker.
func (in *HTTPActiveHealthChecker) DeepCopy() *HTTPActiveHealthChecker {
	if in == nil {
		return nil
	}
	out := new(HTTPActiveHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPClientTimeout) DeepCopyInto(out *HTTPClientTimeout) {
	*out = *in
	if in.RequestReceivedTimeout != nil {
		in, out := &in.RequestReceivedTimeout, &out.RequestReceivedTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.IdleTimeout != nil {
		in, out := &in.IdleTimeout, &out.IdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPClientTimeout.
func (in *HTTPClientTimeout) DeepCopy() *HTTPClientTimeout {
	if in == nil {
		return nil
	}
	out := new(HTTPClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPCredentialInjectionFilter) DeepCopyInto(out *HTTPCredentialInjectionFilter) {
	*out = *in
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(string)
		**out = **in
	}
	if in.Overwrite != nil {
		in, out := &in.Overwrite, &out.Overwrite
		*out = new(bool)
		**out = **in
	}
	in.Credential.DeepCopyInto(&out.Credential)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPCredentialInjectionFilter.
func (in *HTTPCredentialInjectionFilter) DeepCopy() *HTTPCredentialInjectionFilter {
	if in == nil {
		return nil
	}
	out := new(HTTPCredentialInjectionFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPDirectResponseFilter) DeepCopyInto(out *HTTPDirectResponseFilter) {
	*out = *in
	if in.ContentType != nil {
		in, out := &in.ContentType, &out.ContentType
		*out = new(string)
		**out = **in
	}
	if in.Body != nil {
		in, out := &in.Body, &out.Body
		*out = new(CustomResponseBody)
		(*in).DeepCopyInto(*out)
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(int)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPDirectResponseFilter.
func (in *HTTPDirectResponseFilter) DeepCopy() *HTTPDirectResponseFilter {
	if in == nil {
		return nil
	}
	out := new(HTTPDirectResponseFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPExtAuthService) DeepCopyInto(out *HTTPExtAuthService) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.HeadersToBackend != nil {
		in, out := &in.HeadersToBackend, &out.HeadersToBackend
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPExtAuthService.
func (in *HTTPExtAuthService) DeepCopy() *HTTPExtAuthService {
	if in == nil {
		return nil
	}
	out := new(HTTPExtAuthService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPHostnameModifier) DeepCopyInto(out *HTTPHostnameModifier) {
	*out = *in
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPHostnameModifier.
func (in *HTTPHostnameModifier) DeepCopy() *HTTPHostnameModifier {
	if in == nil {
		return nil
	}
	out := new(HTTPHostnameModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPPathModifier) DeepCopyInto(out *HTTPPathModifier) {
	*out = *in
	if in.ReplaceRegexMatch != nil {
		in, out := &in.ReplaceRegexMatch, &out.ReplaceRegexMatch
		*out = new(ReplaceRegexMatch)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPPathModifier.
func (in *HTTPPathModifier) DeepCopy() *HTTPPathModifier {
	if in == nil {
		return nil
	}
	out := new(HTTPPathModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPRouteFilter) DeepCopyInto(out *HTTPRouteFilter) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPRouteFilter.
func (in *HTTPRouteFilter) DeepCopy() *HTTPRouteFilter {
	if in == nil {
		return nil
	}
	out := new(HTTPRouteFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HTTPRouteFilter) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPRouteFilterList) DeepCopyInto(out *HTTPRouteFilterList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]HTTPRouteFilter, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPRouteFilterList.
func (in *HTTPRouteFilterList) DeepCopy() *HTTPRouteFilterList {
	if in == nil {
		return nil
	}
	out := new(HTTPRouteFilterList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HTTPRouteFilterList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPRouteFilterSpec) DeepCopyInto(out *HTTPRouteFilterSpec) {
	*out = *in
	if in.URLRewrite != nil {
		in, out := &in.URLRewrite, &out.URLRewrite
		*out = new(HTTPURLRewriteFilter)
		(*in).DeepCopyInto(*out)
	}
	if in.DirectResponse != nil {
		in, out := &in.DirectResponse, &out.DirectResponse
		*out = new(HTTPDirectResponseFilter)
		(*in).DeepCopyInto(*out)
	}
	if in.CredentialInjection != nil {
		in, out := &in.CredentialInjection, &out.CredentialInjection
		*out = new(HTTPCredentialInjectionFilter)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPRouteFilterSpec.
func (in *HTTPRouteFilterSpec) DeepCopy() *HTTPRouteFilterSpec {
	if in == nil {
		return nil
	}
	out := new(HTTPRouteFilterSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPTimeout) DeepCopyInto(out *HTTPTimeout) {
	*out = *in
	if in.ConnectionIdleTimeout != nil {
		in, out := &in.ConnectionIdleTimeout, &out.ConnectionIdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.MaxConnectionDuration != nil {
		in, out := &in.MaxConnectionDuration, &out.MaxConnectionDuration
		*out = new(v1.Duration)
		**out = **in
	}
	if in.RequestTimeout != nil {
		in, out := &in.RequestTimeout, &out.RequestTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPTimeout.
func (in *HTTPTimeout) DeepCopy() *HTTPTimeout {
	if in == nil {
		return nil
	}
	out := new(HTTPTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPURLRewriteFilter) DeepCopyInto(out *HTTPURLRewriteFilter) {
	*out = *in
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(HTTPHostnameModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(HTTPPathModifier)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPURLRewriteFilter.
func (in *HTTPURLRewriteFilter) DeepCopy() *HTTPURLRewriteFilter {
	if in == nil {
		return nil
	}
	out := new(HTTPURLRewriteFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPWasmCodeSource) DeepCopyInto(out *HTTPWasmCodeSource) {
	*out = *in
	if in.SHA256 != nil {
		in, out := &in.SHA256, &out.SHA256
		*out = new(string)
		**out = **in
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(WasmCodeSourceTLSConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPWasmCodeSource.
func (in *HTTPWasmCodeSource) DeepCopy() *HTTPWasmCodeSource {
	if in == nil {
		return nil
	}
	out := new(HTTPWasmCodeSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Header) DeepCopyInto(out *Header) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Header.
func (in *Header) DeepCopy() *Header {
	if in == nil {
		return nil
	}
	out := new(Header)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderMatch) DeepCopyInto(out *HeaderMatch) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(HeaderMatchType)
		**out = **in
	}
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(string)
		**out = **in
	}
	if in.Invert != nil {
		in, out := &in.Invert, &out.Invert
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderMatch.
func (in *HeaderMatch) DeepCopy() *HeaderMatch {
	if in == nil {
		return nil
	}
	out := new(HeaderMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderSettings) DeepCopyInto(out *HeaderSettings) {
	*out = *in
	if in.EnableEnvoyHeaders != nil {
		in, out := &in.EnableEnvoyHeaders, &out.EnableEnvoyHeaders
		*out = new(bool)
		**out = **in
	}
	if in.DisableRateLimitHeaders != nil {
		in, out := &in.DisableRateLimitHeaders, &out.DisableRateLimitHeaders
		*out = new(bool)
		**out = **in
	}
	if in.XForwardedClientCert != nil {
		in, out := &in.XForwardedClientCert, &out.XForwardedClientCert
		*out = new(XForwardedClientCert)
		(*in).DeepCopyInto(*out)
	}
	if in.WithUnderscoresAction != nil {
		in, out := &in.WithUnderscoresAction, &out.WithUnderscoresAction
		*out = new(WithUnderscoresAction)
		**out = **in
	}
	if in.PreserveXRequestID != nil {
		in, out := &in.PreserveXRequestID, &out.PreserveXRequestID
		*out = new(bool)
		**out = **in
	}
	if in.RequestID != nil {
		in, out := &in.RequestID, &out.RequestID
		*out = new(RequestIDAction)
		**out = **in
	}
	if in.EarlyRequestHeaders != nil {
		in, out := &in.EarlyRequestHeaders, &out.EarlyRequestHeaders
		*out = new(v1.HTTPHeaderFilter)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderSettings.
func (in *HeaderSettings) DeepCopy() *HeaderSettings {
	if in == nil {
		return nil
	}
	out := new(HeaderSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheck) DeepCopyInto(out *HealthCheck) {
	*out = *in
	if in.Active != nil {
		in, out := &in.Active, &out.Active
		*out = new(ActiveHealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.Passive != nil {
		in, out := &in.Passive, &out.Passive
		*out = new(PassiveHealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.PanicThreshold != nil {
		in, out := &in.PanicThreshold, &out.PanicThreshold
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheck.
func (in *HealthCheck) DeepCopy() *HealthCheck {
	if in == nil {
		return nil
	}
	out := new(HealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheckSettings) DeepCopyInto(out *HealthCheckSettings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheckSettings.
func (in *HealthCheckSettings) DeepCopy() *HealthCheckSettings {
	if in == nil {
		return nil
	}
	out := new(HealthCheckSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HostOverrideSettings) DeepCopyInto(out *HostOverrideSettings) {
	*out = *in
	if in.OverrideHostSources != nil {
		in, out := &in.OverrideHostSources, &out.OverrideHostSources
		*out = make([]OverrideHostSource, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HostOverrideSettings.
func (in *HostOverrideSettings) DeepCopy() *HostOverrideSettings {
	if in == nil {
		return nil
	}
	out := new(HostOverrideSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IPEndpoint) DeepCopyInto(out *IPEndpoint) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IPEndpoint.
func (in *IPEndpoint) DeepCopy() *IPEndpoint {
	if in == nil {
		return nil
	}
	out := new(IPEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageWasmCodeSource) DeepCopyInto(out *ImageWasmCodeSource) {
	*out = *in
	if in.SHA256 != nil {
		in, out := &in.SHA256, &out.SHA256
		*out = new(string)
		**out = **in
	}
	if in.PullSecretRef != nil {
		in, out := &in.PullSecretRef, &out.PullSecretRef
		*out = new(v1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(WasmCodeSourceTLSConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageWasmCodeSource.
func (in *ImageWasmCodeSource) DeepCopy() *ImageWasmCodeSource {
	if in == nil {
		return nil
	}
	out := new(ImageWasmCodeSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *InjectedCredential) DeepCopyInto(out *InjectedCredential) {
	*out = *in
	in.ValueRef.DeepCopyInto(&out.ValueRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new InjectedCredential.
func (in *InjectedCredential) DeepCopy() *InjectedCredential {
	if in == nil {
		return nil
	}
	out := new(InjectedCredential)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JSONPatchOperation) DeepCopyInto(out *JSONPatchOperation) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.JSONPath != nil {
		in, out := &in.JSONPath, &out.JSONPath
		*out = new(string)
		**out = **in
	}
	if in.From != nil {
		in, out := &in.From, &out.From
		*out = new(string)
		**out = **in
	}
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(apiextensionsv1.JSON)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JSONPatchOperation.
func (in *JSONPatchOperation) DeepCopy() *JSONPatchOperation {
	if in == nil {
		return nil
	}
	out := new(JSONPatchOperation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWT) DeepCopyInto(out *JWT) {
	*out = *in
	if in.Optional != nil {
		in, out := &in.Optional, &out.Optional
		*out = new(bool)
		**out = **in
	}
	if in.Providers != nil {
		in, out := &in.Providers, &out.Providers
		*out = make([]JWTProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWT.
func (in *JWT) DeepCopy() *JWT {
	if in == nil {
		return nil
	}
	out := new(JWT)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTClaim) DeepCopyInto(out *JWTClaim) {
	*out = *in
	if in.ValueType != nil {
		in, out := &in.ValueType, &out.ValueType
		*out = new(JWTClaimValueType)
		**out = **in
	}
	if in.Values != nil {
		in, out := &in.Values, &out.Values
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTClaim.
func (in *JWTClaim) DeepCopy() *JWTClaim {
	if in == nil {
		return nil
	}
	out := new(JWTClaim)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTExtractor) DeepCopyInto(out *JWTExtractor) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]JWTHeaderExtractor, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Cookies != nil {
		in, out := &in.Cookies, &out.Cookies
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTExtractor.
func (in *JWTExtractor) DeepCopy() *JWTExtractor {
	if in == nil {
		return nil
	}
	out := new(JWTExtractor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTHeaderExtractor) DeepCopyInto(out *JWTHeaderExtractor) {
	*out = *in
	if in.ValuePrefix != nil {
		in, out := &in.ValuePrefix, &out.ValuePrefix
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTHeaderExtractor.
func (in *JWTHeaderExtractor) DeepCopy() *JWTHeaderExtractor {
	if in == nil {
		return nil
	}
	out := new(JWTHeaderExtractor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTPrincipal) DeepCopyInto(out *JWTPrincipal) {
	*out = *in
	if in.Claims != nil {
		in, out := &in.Claims, &out.Claims
		*out = make([]JWTClaim, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Scopes != nil {
		in, out := &in.Scopes, &out.Scopes
		*out = make([]JWTScope, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTPrincipal.
func (in *JWTPrincipal) DeepCopy() *JWTPrincipal {
	if in == nil {
		return nil
	}
	out := new(JWTPrincipal)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTProvider) DeepCopyInto(out *JWTProvider) {
	*out = *in
	if in.Audiences != nil {
		in, out := &in.Audiences, &out.Audiences
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.RemoteJWKS != nil {
		in, out := &in.RemoteJWKS, &out.RemoteJWKS
		*out = new(RemoteJWKS)
		(*in).DeepCopyInto(*out)
	}
	if in.LocalJWKS != nil {
		in, out := &in.LocalJWKS, &out.LocalJWKS
		*out = new(LocalJWKS)
		(*in).DeepCopyInto(*out)
	}
	if in.ClaimToHeaders != nil {
		in, out := &in.ClaimToHeaders, &out.ClaimToHeaders
		*out = make([]ClaimToHeader, len(*in))
		copy(*out, *in)
	}
	if in.RecomputeRoute != nil {
		in, out := &in.RecomputeRoute, &out.RecomputeRoute
		*out = new(bool)
		**out = **in
	}
	if in.ExtractFrom != nil {
		in, out := &in.ExtractFrom, &out.ExtractFrom
		*out = new(JWTExtractor)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTProvider.
func (in *JWTProvider) DeepCopy() *JWTProvider {
	if in == nil {
		return nil
	}
	out := new(JWTProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesClient) DeepCopyInto(out *KubernetesClient) {
	*out = *in
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(KubernetesClientRateLimit)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesClient.
func (in *KubernetesClient) DeepCopy() *KubernetesClient {
	if in == nil {
		return nil
	}
	out := new(KubernetesClient)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesClientRateLimit) DeepCopyInto(out *KubernetesClientRateLimit) {
	*out = *in
	if in.QPS != nil {
		in, out := &in.QPS, &out.QPS
		*out = new(int32)
		**out = **in
	}
	if in.Burst != nil {
		in, out := &in.Burst, &out.Burst
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesClientRateLimit.
func (in *KubernetesClientRateLimit) DeepCopy() *KubernetesClientRateLimit {
	if in == nil {
		return nil
	}
	out := new(KubernetesClientRateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesContainerSpec) DeepCopyInto(out *KubernetesContainerSpec) {
	*out = *in
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]corev1.EnvVar, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(corev1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(corev1.SecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(string)
		**out = **in
	}
	if in.ImageRepository != nil {
		in, out := &in.ImageRepository, &out.ImageRepository
		*out = new(string)
		**out = **in
	}
	if in.VolumeMounts != nil {
		in, out := &in.VolumeMounts, &out.VolumeMounts
		*out = make([]corev1.VolumeMount, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesContainerSpec.
func (in *KubernetesContainerSpec) DeepCopy() *KubernetesContainerSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesContainerSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesDaemonSetSpec) DeepCopyInto(out *KubernetesDaemonSetSpec) {
	*out = *in
	if in.Patch != nil {
		in, out := &in.Patch, &out.Patch
		*out = new(KubernetesPatchSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Strategy != nil {
		in, out := &in.Strategy, &out.Strategy
		*out = new(appsv1.DaemonSetUpdateStrategy)
		(*in).DeepCopyInto(*out)
	}
	if in.Pod != nil {
		in, out := &in.Pod, &out.Pod
		*out = new(KubernetesPodSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Container != nil {
		in, out := &in.Container, &out.Container
		*out = new(KubernetesContainerSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesDaemonSetSpec.
func (in *KubernetesDaemonSetSpec) DeepCopy() *KubernetesDaemonSetSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesDaemonSetSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesDeployMode) DeepCopyInto(out *KubernetesDeployMode) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(KubernetesDeployModeType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesDeployMode.
func (in *KubernetesDeployMode) DeepCopy() *KubernetesDeployMode {
	if in == nil {
		return nil
	}
	out := new(KubernetesDeployMode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesDeploymentSpec) DeepCopyInto(out *KubernetesDeploymentSpec) {
	*out = *in
	if in.Patch != nil {
		in, out := &in.Patch, &out.Patch
		*out = new(KubernetesPatchSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(int32)
		**out = **in
	}
	if in.Strategy != nil {
		in, out := &in.Strategy, &out.Strategy
		*out = new(appsv1.DeploymentStrategy)
		(*in).DeepCopyInto(*out)
	}
	if in.Pod != nil {
		in, out := &in.Pod, &out.Pod
		*out = new(KubernetesPodSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Container != nil {
		in, out := &in.Container, &out.Container
		*out = new(KubernetesContainerSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.InitContainers != nil {
		in, out := &in.InitContainers, &out.InitContainers
		*out = make([]corev1.Container, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesDeploymentSpec.
func (in *KubernetesDeploymentSpec) DeepCopy() *KubernetesDeploymentSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesDeploymentSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesHorizontalPodAutoscalerSpec) DeepCopyInto(out *KubernetesHorizontalPodAutoscalerSpec) {
	*out = *in
	if in.MinReplicas != nil {
		in, out := &in.MinReplicas, &out.MinReplicas
		*out = new(int32)
		**out = **in
	}
	if in.MaxReplicas != nil {
		in, out := &in.MaxReplicas, &out.MaxReplicas
		*out = new(int32)
		**out = **in
	}
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = make([]v2.MetricSpec, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Behavior != nil {
		in, out := &in.Behavior, &out.Behavior
		*out = new(v2.HorizontalPodAutoscalerBehavior)
		(*in).DeepCopyInto(*out)
	}
	if in.Patch != nil {
		in, out := &in.Patch, &out.Patch
		*out = new(KubernetesPatchSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesHorizontalPodAutoscalerSpec.
func (in *KubernetesHorizontalPodAutoscalerSpec) DeepCopy() *KubernetesHorizontalPodAutoscalerSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesHorizontalPodAutoscalerSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesPatchSpec) DeepCopyInto(out *KubernetesPatchSpec) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(MergeType)
		**out = **in
	}
	in.Value.DeepCopyInto(&out.Value)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesPatchSpec.
func (in *KubernetesPatchSpec) DeepCopy() *KubernetesPatchSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesPatchSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesPodDisruptionBudgetSpec) DeepCopyInto(out *KubernetesPodDisruptionBudgetSpec) {
	*out = *in
	if in.MinAvailable != nil {
		in, out := &in.MinAvailable, &out.MinAvailable
		*out = new(intstr.IntOrString)
		**out = **in
	}
	if in.MaxUnavailable != nil {
		in, out := &in.MaxUnavailable, &out.MaxUnavailable
		*out = new(intstr.IntOrString)
		**out = **in
	}
	if in.Patch != nil {
		in, out := &in.Patch, &out.Patch
		*out = new(KubernetesPatchSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesPodDisruptionBudgetSpec.
func (in *KubernetesPodDisruptionBudgetSpec) DeepCopy() *KubernetesPodDisruptionBudgetSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesPodDisruptionBudgetSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesPodSpec) DeepCopyInto(out *KubernetesPodSpec) {
	*out = *in
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(corev1.PodSecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Affinity != nil {
		in, out := &in.Affinity, &out.Affinity
		*out = new(corev1.Affinity)
		(*in).DeepCopyInto(*out)
	}
	if in.Tolerations != nil {
		in, out := &in.Tolerations, &out.Tolerations
		*out = make([]corev1.Toleration, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Volumes != nil {
		in, out := &in.Volumes, &out.Volumes
		*out = make([]corev1.Volume, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ImagePullSecrets != nil {
		in, out := &in.ImagePullSecrets, &out.ImagePullSecrets
		*out = make([]corev1.LocalObjectReference, len(*in))
		copy(*out, *in)
	}
	if in.NodeSelector != nil {
		in, out := &in.NodeSelector, &out.NodeSelector
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.TopologySpreadConstraints != nil {
		in, out := &in.TopologySpreadConstraints, &out.TopologySpreadConstraints
		*out = make([]corev1.TopologySpreadConstraint, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesPodSpec.
func (in *KubernetesPodSpec) DeepCopy() *KubernetesPodSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesPodSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesServiceAccountSpec) DeepCopyInto(out *KubernetesServiceAccountSpec) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesServiceAccountSpec.
func (in *KubernetesServiceAccountSpec) DeepCopy() *KubernetesServiceAccountSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesServiceAccountSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesServiceSpec) DeepCopyInto(out *KubernetesServiceSpec) {
	*out = *in
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(ServiceType)
		**out = **in
	}
	if in.LoadBalancerClass != nil {
		in, out := &in.LoadBalancerClass, &out.LoadBalancerClass
		*out = new(string)
		**out = **in
	}
	if in.AllocateLoadBalancerNodePorts != nil {
		in, out := &in.AllocateLoadBalancerNodePorts, &out.AllocateLoadBalancerNodePorts
		*out = new(bool)
		**out = **in
	}
	if in.LoadBalancerSourceRanges != nil {
		in, out := &in.LoadBalancerSourceRanges, &out.LoadBalancerSourceRanges
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.LoadBalancerIP != nil {
		in, out := &in.LoadBalancerIP, &out.LoadBalancerIP
		*out = new(string)
		**out = **in
	}
	if in.ExternalTrafficPolicy != nil {
		in, out := &in.ExternalTrafficPolicy, &out.ExternalTrafficPolicy
		*out = new(ServiceExternalTrafficPolicy)
		**out = **in
	}
	if in.Patch != nil {
		in, out := &in.Patch, &out.Patch
		*out = new(KubernetesPatchSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesServiceSpec.
func (in *KubernetesServiceSpec) DeepCopy() *KubernetesServiceSpec {
	if in == nil {
		return nil
	}
	out := new(KubernetesServiceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesWatchMode) DeepCopyInto(out *KubernetesWatchMode) {
	*out = *in
	if in.Namespaces != nil {
		in, out := &in.Namespaces, &out.Namespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.NamespaceSelector != nil {
		in, out := &in.NamespaceSelector, &out.NamespaceSelector
		*out = new(metav1.LabelSelector)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesWatchMode.
func (in *KubernetesWatchMode) DeepCopy() *KubernetesWatchMode {
	if in == nil {
		return nil
	}
	out := new(KubernetesWatchMode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LeaderElection) DeepCopyInto(out *LeaderElection) {
	*out = *in
	if in.LeaseDuration != nil {
		in, out := &in.LeaseDuration, &out.LeaseDuration
		*out = new(v1.Duration)
		**out = **in
	}
	if in.RenewDeadline != nil {
		in, out := &in.RenewDeadline, &out.RenewDeadline
		*out = new(v1.Duration)
		**out = **in
	}
	if in.RetryPeriod != nil {
		in, out := &in.RetryPeriod, &out.RetryPeriod
		*out = new(v1.Duration)
		**out = **in
	}
	if in.Disable != nil {
		in, out := &in.Disable, &out.Disable
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LeaderElection.
func (in *LeaderElection) DeepCopy() *LeaderElection {
	if in == nil {
		return nil
	}
	out := new(LeaderElection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LiteralCustomTag) DeepCopyInto(out *LiteralCustomTag) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LiteralCustomTag.
func (in *LiteralCustomTag) DeepCopy() *LiteralCustomTag {
	if in == nil {
		return nil
	}
	out := new(LiteralCustomTag)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LoadBalancer) DeepCopyInto(out *LoadBalancer) {
	*out = *in
	if in.ConsistentHash != nil {
		in, out := &in.ConsistentHash, &out.ConsistentHash
		*out = new(ConsistentHash)
		(*in).DeepCopyInto(*out)
	}
	if in.SlowStart != nil {
		in, out := &in.SlowStart, &out.SlowStart
		*out = new(SlowStart)
		(*in).DeepCopyInto(*out)
	}
	if in.ZoneAware != nil {
		in, out := &in.ZoneAware, &out.ZoneAware
		*out = new(ZoneAware)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LoadBalancer.
func (in *LoadBalancer) DeepCopy() *LoadBalancer {
	if in == nil {
		return nil
	}
	out := new(LoadBalancer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalJWKS) DeepCopyInto(out *LocalJWKS) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(LocalJWKSType)
		**out = **in
	}
	if in.Inline != nil {
		in, out := &in.Inline, &out.Inline
		*out = new(string)
		**out = **in
	}
	if in.ValueRef != nil {
		in, out := &in.ValueRef, &out.ValueRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalJWKS.
func (in *LocalJWKS) DeepCopy() *LocalJWKS {
	if in == nil {
		return nil
	}
	out := new(LocalJWKS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalRateLimit) DeepCopyInto(out *LocalRateLimit) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]RateLimitRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalRateLimit.
func (in *LocalRateLimit) DeepCopy() *LocalRateLimit {
	if in == nil {
		return nil
	}
	out := new(LocalRateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Lua) DeepCopyInto(out *Lua) {
	*out = *in
	if in.Inline != nil {
		in, out := &in.Inline, &out.Inline
		*out = new(string)
		**out = **in
	}
	if in.ValueRef != nil {
		in, out := &in.ValueRef, &out.ValueRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Lua.
func (in *Lua) DeepCopy() *Lua {
	if in == nil {
		return nil
	}
	out := new(Lua)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MetadataKey) DeepCopyInto(out *MetadataKey) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = make([]MetadataPathSegment, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MetadataKey.
func (in *MetadataKey) DeepCopy() *MetadataKey {
	if in == nil {
		return nil
	}
	out := new(MetadataKey)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MetadataPathSegment) DeepCopyInto(out *MetadataPathSegment) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MetadataPathSegment.
func (in *MetadataPathSegment) DeepCopy() *MetadataPathSegment {
	if in == nil {
		return nil
	}
	out := new(MetadataPathSegment)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDC) DeepCopyInto(out *OIDC) {
	*out = *in
	in.Provider.DeepCopyInto(&out.Provider)
	in.ClientSecret.DeepCopyInto(&out.ClientSecret)
	if in.CookieNames != nil {
		in, out := &in.CookieNames, &out.CookieNames
		*out = new(OIDCCookieNames)
		(*in).DeepCopyInto(*out)
	}
	if in.CookieConfig != nil {
		in, out := &in.CookieConfig, &out.CookieConfig
		*out = new(OIDCCookieConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.CookieDomain != nil {
		in, out := &in.CookieDomain, &out.CookieDomain
		*out = new(string)
		**out = **in
	}
	if in.Scopes != nil {
		in, out := &in.Scopes, &out.Scopes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.RedirectURL != nil {
		in, out := &in.RedirectURL, &out.RedirectURL
		*out = new(string)
		**out = **in
	}
	if in.DenyRedirect != nil {
		in, out := &in.DenyRedirect, &out.DenyRedirect
		*out = new(OIDCDenyRedirect)
		(*in).DeepCopyInto(*out)
	}
	if in.LogoutPath != nil {
		in, out := &in.LogoutPath, &out.LogoutPath
		*out = new(string)
		**out = **in
	}
	if in.ForwardAccessToken != nil {
		in, out := &in.ForwardAccessToken, &out.ForwardAccessToken
		*out = new(bool)
		**out = **in
	}
	if in.DefaultTokenTTL != nil {
		in, out := &in.DefaultTokenTTL, &out.DefaultTokenTTL
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.RefreshToken != nil {
		in, out := &in.RefreshToken, &out.RefreshToken
		*out = new(bool)
		**out = **in
	}
	if in.DefaultRefreshTokenTTL != nil {
		in, out := &in.DefaultRefreshTokenTTL, &out.DefaultRefreshTokenTTL
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.PassThroughAuthHeader != nil {
		in, out := &in.PassThroughAuthHeader, &out.PassThroughAuthHeader
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDC.
func (in *OIDC) DeepCopy() *OIDC {
	if in == nil {
		return nil
	}
	out := new(OIDC)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCCookieConfig) DeepCopyInto(out *OIDCCookieConfig) {
	*out = *in
	if in.SameSite != nil {
		in, out := &in.SameSite, &out.SameSite
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCCookieConfig.
func (in *OIDCCookieConfig) DeepCopy() *OIDCCookieConfig {
	if in == nil {
		return nil
	}
	out := new(OIDCCookieConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCCookieNames) DeepCopyInto(out *OIDCCookieNames) {
	*out = *in
	if in.AccessToken != nil {
		in, out := &in.AccessToken, &out.AccessToken
		*out = new(string)
		**out = **in
	}
	if in.IDToken != nil {
		in, out := &in.IDToken, &out.IDToken
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCCookieNames.
func (in *OIDCCookieNames) DeepCopy() *OIDCCookieNames {
	if in == nil {
		return nil
	}
	out := new(OIDCCookieNames)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCDenyRedirect) DeepCopyInto(out *OIDCDenyRedirect) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]OIDCDenyRedirectHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCDenyRedirect.
func (in *OIDCDenyRedirect) DeepCopy() *OIDCDenyRedirect {
	if in == nil {
		return nil
	}
	out := new(OIDCDenyRedirect)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCDenyRedirectHeader) DeepCopyInto(out *OIDCDenyRedirectHeader) {
	*out = *in
	in.StringMatch.DeepCopyInto(&out.StringMatch)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCDenyRedirectHeader.
func (in *OIDCDenyRedirectHeader) DeepCopy() *OIDCDenyRedirectHeader {
	if in == nil {
		return nil
	}
	out := new(OIDCDenyRedirectHeader)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCProvider) DeepCopyInto(out *OIDCProvider) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.AuthorizationEndpoint != nil {
		in, out := &in.AuthorizationEndpoint, &out.AuthorizationEndpoint
		*out = new(string)
		**out = **in
	}
	if in.TokenEndpoint != nil {
		in, out := &in.TokenEndpoint, &out.TokenEndpoint
		*out = new(string)
		**out = **in
	}
	if in.EndSessionEndpoint != nil {
		in, out := &in.EndSessionEndpoint, &out.EndSessionEndpoint
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCProvider.
func (in *OIDCProvider) DeepCopy() *OIDCProvider {
	if in == nil {
		return nil
	}
	out := new(OIDCProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OpenTelemetryEnvoyProxyAccessLog) DeepCopyInto(out *OpenTelemetryEnvoyProxyAccessLog) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.Host != nil {
		in, out := &in.Host, &out.Host
		*out = new(string)
		**out = **in
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OpenTelemetryEnvoyProxyAccessLog.
func (in *OpenTelemetryEnvoyProxyAccessLog) DeepCopy() *OpenTelemetryEnvoyProxyAccessLog {
	if in == nil {
		return nil
	}
	out := new(OpenTelemetryEnvoyProxyAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Operation) DeepCopyInto(out *Operation) {
	*out = *in
	if in.Methods != nil {
		in, out := &in.Methods, &out.Methods
		*out = make([]v1.HTTPMethod, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Operation.
func (in *Operation) DeepCopy() *Operation {
	if in == nil {
		return nil
	}
	out := new(Operation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OverrideHostSource) DeepCopyInto(out *OverrideHostSource) {
	*out = *in
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(string)
		**out = **in
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(MetadataKey)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OverrideHostSource.
func (in *OverrideHostSource) DeepCopy() *OverrideHostSource {
	if in == nil {
		return nil
	}
	out := new(OverrideHostSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PassiveHealthCheck) DeepCopyInto(out *PassiveHealthCheck) {
	*out = *in
	if in.SplitExternalLocalOriginErrors != nil {
		in, out := &in.SplitExternalLocalOriginErrors, &out.SplitExternalLocalOriginErrors
		*out = new(bool)
		**out = **in
	}
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.ConsecutiveLocalOriginFailures != nil {
		in, out := &in.ConsecutiveLocalOriginFailures, &out.ConsecutiveLocalOriginFailures
		*out = new(uint32)
		**out = **in
	}
	if in.ConsecutiveGatewayErrors != nil {
		in, out := &in.ConsecutiveGatewayErrors, &out.ConsecutiveGatewayErrors
		*out = new(uint32)
		**out = **in
	}
	if in.Consecutive5xxErrors != nil {
		in, out := &in.Consecutive5xxErrors, &out.Consecutive5xxErrors
		*out = new(uint32)
		**out = **in
	}
	if in.BaseEjectionTime != nil {
		in, out := &in.BaseEjectionTime, &out.BaseEjectionTime
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.MaxEjectionPercent != nil {
		in, out := &in.MaxEjectionPercent, &out.MaxEjectionPercent
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PassiveHealthCheck.
func (in *PassiveHealthCheck) DeepCopy() *PassiveHealthCheck {
	if in == nil {
		return nil
	}
	out := new(PassiveHealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PathSettings) DeepCopyInto(out *PathSettings) {
	*out = *in
	if in.EscapedSlashesAction != nil {
		in, out := &in.EscapedSlashesAction, &out.EscapedSlashesAction
		*out = new(PathEscapedSlashAction)
		**out = **in
	}
	if in.DisableMergeSlashes != nil {
		in, out := &in.DisableMergeSlashes, &out.DisableMergeSlashes
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PathSettings.
func (in *PathSettings) DeepCopy() *PathSettings {
	if in == nil {
		return nil
	}
	out := new(PathSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PerEndpointCircuitBreakers) DeepCopyInto(out *PerEndpointCircuitBreakers) {
	*out = *in
	if in.MaxConnections != nil {
		in, out := &in.MaxConnections, &out.MaxConnections
		*out = new(int64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PerEndpointCircuitBreakers.
func (in *PerEndpointCircuitBreakers) DeepCopy() *PerEndpointCircuitBreakers {
	if in == nil {
		return nil
	}
	out := new(PerEndpointCircuitBreakers)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PerRetryPolicy) DeepCopyInto(out *PerRetryPolicy) {
	*out = *in
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.BackOff != nil {
		in, out := &in.BackOff, &out.BackOff
		*out = new(BackOffPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PerRetryPolicy.
func (in *PerRetryPolicy) DeepCopy() *PerRetryPolicy {
	if in == nil {
		return nil
	}
	out := new(PerRetryPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyTargetReferences) DeepCopyInto(out *PolicyTargetReferences) {
	*out = *in
	if in.TargetRef != nil {
		in, out := &in.TargetRef, &out.TargetRef
		*out = new(v1alpha2.LocalPolicyTargetReferenceWithSectionName)
		(*in).DeepCopyInto(*out)
	}
	if in.TargetRefs != nil {
		in, out := &in.TargetRefs, &out.TargetRefs
		*out = make([]v1alpha2.LocalPolicyTargetReferenceWithSectionName, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.TargetSelectors != nil {
		in, out := &in.TargetSelectors, &out.TargetSelectors
		*out = make([]TargetSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyTargetReferences.
func (in *PolicyTargetReferences) DeepCopy() *PolicyTargetReferences {
	if in == nil {
		return nil
	}
	out := new(PolicyTargetReferences)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PreferLocalZone) DeepCopyInto(out *PreferLocalZone) {
	*out = *in
	if in.Force != nil {
		in, out := &in.Force, &out.Force
		*out = new(ForceLocalZone)
		(*in).DeepCopyInto(*out)
	}
	if in.MinEndpointsThreshold != nil {
		in, out := &in.MinEndpointsThreshold, &out.MinEndpointsThreshold
		*out = new(uint64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PreferLocalZone.
func (in *PreferLocalZone) DeepCopy() *PreferLocalZone {
	if in == nil {
		return nil
	}
	out := new(PreferLocalZone)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Principal) DeepCopyInto(out *Principal) {
	*out = *in
	if in.ClientCIDRs != nil {
		in, out := &in.ClientCIDRs, &out.ClientCIDRs
		*out = make([]CIDR, len(*in))
		copy(*out, *in)
	}
	if in.JWT != nil {
		in, out := &in.JWT, &out.JWT
		*out = new(JWTPrincipal)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]AuthorizationHeaderMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Principal.
func (in *Principal) DeepCopy() *Principal {
	if in == nil {
		return nil
	}
	out := new(Principal)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProcessingModeOptions) DeepCopyInto(out *ProcessingModeOptions) {
	*out = *in
	if in.Body != nil {
		in, out := &in.Body, &out.Body
		*out = new(ExtProcBodyProcessingMode)
		**out = **in
	}
	if in.Attributes != nil {
		in, out := &in.Attributes, &out.Attributes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProcessingModeOptions.
func (in *ProcessingModeOptions) DeepCopy() *ProcessingModeOptions {
	if in == nil {
		return nil
	}
	out := new(ProcessingModeOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProtocolUpgradeConfig) DeepCopyInto(out *ProtocolUpgradeConfig) {
	*out = *in
	if in.Connect != nil {
		in, out := &in.Connect, &out.Connect
		*out = new(ConnectConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProtocolUpgradeConfig.
func (in *ProtocolUpgradeConfig) DeepCopy() *ProtocolUpgradeConfig {
	if in == nil {
		return nil
	}
	out := new(ProtocolUpgradeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyAccessLog) DeepCopyInto(out *ProxyAccessLog) {
	*out = *in
	if in.Disable != nil {
		in, out := &in.Disable, &out.Disable
		*out = new(bool)
		**out = **in
	}
	if in.Settings != nil {
		in, out := &in.Settings, &out.Settings
		*out = make([]ProxyAccessLogSetting, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyAccessLog.
func (in *ProxyAccessLog) DeepCopy() *ProxyAccessLog {
	if in == nil {
		return nil
	}
	out := new(ProxyAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyAccessLogFormat) DeepCopyInto(out *ProxyAccessLogFormat) {
	*out = *in
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = new(string)
		**out = **in
	}
	if in.JSON != nil {
		in, out := &in.JSON, &out.JSON
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyAccessLogFormat.
func (in *ProxyAccessLogFormat) DeepCopy() *ProxyAccessLogFormat {
	if in == nil {
		return nil
	}
	out := new(ProxyAccessLogFormat)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyAccessLogSetting) DeepCopyInto(out *ProxyAccessLogSetting) {
	*out = *in
	if in.Format != nil {
		in, out := &in.Format, &out.Format
		*out = new(ProxyAccessLogFormat)
		(*in).DeepCopyInto(*out)
	}
	if in.Matches != nil {
		in, out := &in.Matches, &out.Matches
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Sinks != nil {
		in, out := &in.Sinks, &out.Sinks
		*out = make([]ProxyAccessLogSink, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(ProxyAccessLogType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyAccessLogSetting.
func (in *ProxyAccessLogSetting) DeepCopy() *ProxyAccessLogSetting {
	if in == nil {
		return nil
	}
	out := new(ProxyAccessLogSetting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyAccessLogSink) DeepCopyInto(out *ProxyAccessLogSink) {
	*out = *in
	if in.ALS != nil {
		in, out := &in.ALS, &out.ALS
		*out = new(ALSEnvoyProxyAccessLog)
		(*in).DeepCopyInto(*out)
	}
	if in.File != nil {
		in, out := &in.File, &out.File
		*out = new(FileEnvoyProxyAccessLog)
		**out = **in
	}
	if in.OpenTelemetry != nil {
		in, out := &in.OpenTelemetry, &out.OpenTelemetry
		*out = new(OpenTelemetryEnvoyProxyAccessLog)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyAccessLogSink.
func (in *ProxyAccessLogSink) DeepCopy() *ProxyAccessLogSink {
	if in == nil {
		return nil
	}
	out := new(ProxyAccessLogSink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyBootstrap) DeepCopyInto(out *ProxyBootstrap) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(BootstrapType)
		**out = **in
	}
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(string)
		**out = **in
	}
	if in.JSONPatches != nil {
		in, out := &in.JSONPatches, &out.JSONPatches
		*out = make([]JSONPatchOperation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyBootstrap.
func (in *ProxyBootstrap) DeepCopy() *ProxyBootstrap {
	if in == nil {
		return nil
	}
	out := new(ProxyBootstrap)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyLogging) DeepCopyInto(out *ProxyLogging) {
	*out = *in
	if in.Level != nil {
		in, out := &in.Level, &out.Level
		*out = make(map[ProxyLogComponent]LogLevel, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyLogging.
func (in *ProxyLogging) DeepCopy() *ProxyLogging {
	if in == nil {
		return nil
	}
	out := new(ProxyLogging)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyMetricSink) DeepCopyInto(out *ProxyMetricSink) {
	*out = *in
	if in.OpenTelemetry != nil {
		in, out := &in.OpenTelemetry, &out.OpenTelemetry
		*out = new(ProxyOpenTelemetrySink)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyMetricSink.
func (in *ProxyMetricSink) DeepCopy() *ProxyMetricSink {
	if in == nil {
		return nil
	}
	out := new(ProxyMetricSink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyMetrics) DeepCopyInto(out *ProxyMetrics) {
	*out = *in
	if in.Prometheus != nil {
		in, out := &in.Prometheus, &out.Prometheus
		*out = new(ProxyPrometheusProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.Sinks != nil {
		in, out := &in.Sinks, &out.Sinks
		*out = make([]ProxyMetricSink, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Matches != nil {
		in, out := &in.Matches, &out.Matches
		*out = make([]StringMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.EnableVirtualHostStats != nil {
		in, out := &in.EnableVirtualHostStats, &out.EnableVirtualHostStats
		*out = new(bool)
		**out = **in
	}
	if in.EnablePerEndpointStats != nil {
		in, out := &in.EnablePerEndpointStats, &out.EnablePerEndpointStats
		*out = new(bool)
		**out = **in
	}
	if in.EnableRequestResponseSizesStats != nil {
		in, out := &in.EnableRequestResponseSizesStats, &out.EnableRequestResponseSizesStats
		*out = new(bool)
		**out = **in
	}
	if in.ClusterStatName != nil {
		in, out := &in.ClusterStatName, &out.ClusterStatName
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyMetrics.
func (in *ProxyMetrics) DeepCopy() *ProxyMetrics {
	if in == nil {
		return nil
	}
	out := new(ProxyMetrics)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyOpenTelemetrySink) DeepCopyInto(out *ProxyOpenTelemetrySink) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.Host != nil {
		in, out := &in.Host, &out.Host
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyOpenTelemetrySink.
func (in *ProxyOpenTelemetrySink) DeepCopy() *ProxyOpenTelemetrySink {
	if in == nil {
		return nil
	}
	out := new(ProxyOpenTelemetrySink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyPrometheusProvider) DeepCopyInto(out *ProxyPrometheusProvider) {
	*out = *in
	if in.Compression != nil {
		in, out := &in.Compression, &out.Compression
		*out = new(Compression)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyPrometheusProvider.
func (in *ProxyPrometheusProvider) DeepCopy() *ProxyPrometheusProvider {
	if in == nil {
		return nil
	}
	out := new(ProxyPrometheusProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyProtocol) DeepCopyInto(out *ProxyProtocol) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyProtocol.
func (in *ProxyProtocol) DeepCopy() *ProxyProtocol {
	if in == nil {
		return nil
	}
	out := new(ProxyProtocol)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyTelemetry) DeepCopyInto(out *ProxyTelemetry) {
	*out = *in
	if in.AccessLog != nil {
		in, out := &in.AccessLog, &out.AccessLog
		*out = new(ProxyAccessLog)
		(*in).DeepCopyInto(*out)
	}
	if in.Tracing != nil {
		in, out := &in.Tracing, &out.Tracing
		*out = new(ProxyTracing)
		(*in).DeepCopyInto(*out)
	}
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = new(ProxyMetrics)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyTelemetry.
func (in *ProxyTelemetry) DeepCopy() *ProxyTelemetry {
	if in == nil {
		return nil
	}
	out := new(ProxyTelemetry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyTracing) DeepCopyInto(out *ProxyTracing) {
	*out = *in
	if in.SamplingRate != nil {
		in, out := &in.SamplingRate, &out.SamplingRate
		*out = new(uint32)
		**out = **in
	}
	if in.SamplingFraction != nil {
		in, out := &in.SamplingFraction, &out.SamplingFraction
		*out = new(v1.Fraction)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomTags != nil {
		in, out := &in.CustomTags, &out.CustomTags
		*out = make(map[string]CustomTag, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	in.Provider.DeepCopyInto(&out.Provider)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyTracing.
func (in *ProxyTracing) DeepCopy() *ProxyTracing {
	if in == nil {
		return nil
	}
	out := new(ProxyTracing)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimit) DeepCopyInto(out *RateLimit) {
	*out = *in
	in.Backend.DeepCopyInto(&out.Backend)
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.Telemetry != nil {
		in, out := &in.Telemetry, &out.Telemetry
		*out = new(RateLimitTelemetry)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimit.
func (in *RateLimit) DeepCopy() *RateLimit {
	if in == nil {
		return nil
	}
	out := new(RateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitCost) DeepCopyInto(out *RateLimitCost) {
	*out = *in
	if in.Request != nil {
		in, out := &in.Request, &out.Request
		*out = new(RateLimitCostSpecifier)
		(*in).DeepCopyInto(*out)
	}
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(RateLimitCostSpecifier)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitCost.
func (in *RateLimitCost) DeepCopy() *RateLimitCost {
	if in == nil {
		return nil
	}
	out := new(RateLimitCost)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitCostMetadata) DeepCopyInto(out *RateLimitCostMetadata) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitCostMetadata.
func (in *RateLimitCostMetadata) DeepCopy() *RateLimitCostMetadata {
	if in == nil {
		return nil
	}
	out := new(RateLimitCostMetadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitCostSpecifier) DeepCopyInto(out *RateLimitCostSpecifier) {
	*out = *in
	if in.Number != nil {
		in, out := &in.Number, &out.Number
		*out = new(uint64)
		**out = **in
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(RateLimitCostMetadata)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitCostSpecifier.
func (in *RateLimitCostSpecifier) DeepCopy() *RateLimitCostSpecifier {
	if in == nil {
		return nil
	}
	out := new(RateLimitCostSpecifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitDatabaseBackend) DeepCopyInto(out *RateLimitDatabaseBackend) {
	*out = *in
	if in.Redis != nil {
		in, out := &in.Redis, &out.Redis
		*out = new(RateLimitRedisSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitDatabaseBackend.
func (in *RateLimitDatabaseBackend) DeepCopy() *RateLimitDatabaseBackend {
	if in == nil {
		return nil
	}
	out := new(RateLimitDatabaseBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitMetrics) DeepCopyInto(out *RateLimitMetrics) {
	*out = *in
	if in.Prometheus != nil {
		in, out := &in.Prometheus, &out.Prometheus
		*out = new(RateLimitMetricsPrometheusProvider)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitMetrics.
func (in *RateLimitMetrics) DeepCopy() *RateLimitMetrics {
	if in == nil {
		return nil
	}
	out := new(RateLimitMetrics)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitMetricsPrometheusProvider) DeepCopyInto(out *RateLimitMetricsPrometheusProvider) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitMetricsPrometheusProvider.
func (in *RateLimitMetricsPrometheusProvider) DeepCopy() *RateLimitMetricsPrometheusProvider {
	if in == nil {
		return nil
	}
	out := new(RateLimitMetricsPrometheusProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitRedisSettings) DeepCopyInto(out *RateLimitRedisSettings) {
	*out = *in
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(RedisTLSSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitRedisSettings.
func (in *RateLimitRedisSettings) DeepCopy() *RateLimitRedisSettings {
	if in == nil {
		return nil
	}
	out := new(RateLimitRedisSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitRule) DeepCopyInto(out *RateLimitRule) {
	*out = *in
	if in.ClientSelectors != nil {
		in, out := &in.ClientSelectors, &out.ClientSelectors
		*out = make([]RateLimitSelectCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	out.Limit = in.Limit
	if in.Cost != nil {
		in, out := &in.Cost, &out.Cost
		*out = new(RateLimitCost)
		(*in).DeepCopyInto(*out)
	}
	if in.Shared != nil {
		in, out := &in.Shared, &out.Shared
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitRule.
func (in *RateLimitRule) DeepCopy() *RateLimitRule {
	if in == nil {
		return nil
	}
	out := new(RateLimitRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitSelectCondition) DeepCopyInto(out *RateLimitSelectCondition) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]HeaderMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.SourceCIDR != nil {
		in, out := &in.SourceCIDR, &out.SourceCIDR
		*out = new(SourceMatch)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitSelectCondition.
func (in *RateLimitSelectCondition) DeepCopy() *RateLimitSelectCondition {
	if in == nil {
		return nil
	}
	out := new(RateLimitSelectCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitSpec) DeepCopyInto(out *RateLimitSpec) {
	*out = *in
	if in.Global != nil {
		in, out := &in.Global, &out.Global
		*out = new(GlobalRateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.Local != nil {
		in, out := &in.Local, &out.Local
		*out = new(LocalRateLimit)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitSpec.
func (in *RateLimitSpec) DeepCopy() *RateLimitSpec {
	if in == nil {
		return nil
	}
	out := new(RateLimitSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitTelemetry) DeepCopyInto(out *RateLimitTelemetry) {
	*out = *in
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = new(RateLimitMetrics)
		(*in).DeepCopyInto(*out)
	}
	if in.Tracing != nil {
		in, out := &in.Tracing, &out.Tracing
		*out = new(RateLimitTracing)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitTelemetry.
func (in *RateLimitTelemetry) DeepCopy() *RateLimitTelemetry {
	if in == nil {
		return nil
	}
	out := new(RateLimitTelemetry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitTracing) DeepCopyInto(out *RateLimitTracing) {
	*out = *in
	if in.SamplingRate != nil {
		in, out := &in.SamplingRate, &out.SamplingRate
		*out = new(uint32)
		**out = **in
	}
	if in.Provider != nil {
		in, out := &in.Provider, &out.Provider
		*out = new(RateLimitTracingProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitTracing.
func (in *RateLimitTracing) DeepCopy() *RateLimitTracing {
	if in == nil {
		return nil
	}
	out := new(RateLimitTracing)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitTracingProvider) DeepCopyInto(out *RateLimitTracingProvider) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(RateLimitTracingProviderType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitTracingProvider.
func (in *RateLimitTracingProvider) DeepCopy() *RateLimitTracingProvider {
	if in == nil {
		return nil
	}
	out := new(RateLimitTracingProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitValue) DeepCopyInto(out *RateLimitValue) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitValue.
func (in *RateLimitValue) DeepCopy() *RateLimitValue {
	if in == nil {
		return nil
	}
	out := new(RateLimitValue)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RedisTLSSettings) DeepCopyInto(out *RedisTLSSettings) {
	*out = *in
	if in.CertificateRef != nil {
		in, out := &in.CertificateRef, &out.CertificateRef
		*out = new(v1.SecretObjectReference)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RedisTLSSettings.
func (in *RedisTLSSettings) DeepCopy() *RedisTLSSettings {
	if in == nil {
		return nil
	}
	out := new(RedisTLSSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemoteJWKS) DeepCopyInto(out *RemoteJWKS) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemoteJWKS.
func (in *RemoteJWKS) DeepCopy() *RemoteJWKS {
	if in == nil {
		return nil
	}
	out := new(RemoteJWKS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReplaceRegexMatch) DeepCopyInto(out *ReplaceRegexMatch) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReplaceRegexMatch.
func (in *ReplaceRegexMatch) DeepCopy() *ReplaceRegexMatch {
	if in == nil {
		return nil
	}
	out := new(ReplaceRegexMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RequestBuffer) DeepCopyInto(out *RequestBuffer) {
	*out = *in
	out.Limit = in.Limit.DeepCopy()
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RequestBuffer.
func (in *RequestBuffer) DeepCopy() *RequestBuffer {
	if in == nil {
		return nil
	}
	out := new(RequestBuffer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RequestHeaderCustomTag) DeepCopyInto(out *RequestHeaderCustomTag) {
	*out = *in
	if in.DefaultValue != nil {
		in, out := &in.DefaultValue, &out.DefaultValue
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RequestHeaderCustomTag.
func (in *RequestHeaderCustomTag) DeepCopy() *RequestHeaderCustomTag {
	if in == nil {
		return nil
	}
	out := new(RequestHeaderCustomTag)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResponseOverride) DeepCopyInto(out *ResponseOverride) {
	*out = *in
	in.Match.DeepCopyInto(&out.Match)
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(CustomResponse)
		(*in).DeepCopyInto(*out)
	}
	if in.Redirect != nil {
		in, out := &in.Redirect, &out.Redirect
		*out = new(CustomRedirect)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResponseOverride.
func (in *ResponseOverride) DeepCopy() *ResponseOverride {
	if in == nil {
		return nil
	}
	out := new(ResponseOverride)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Retry) DeepCopyInto(out *Retry) {
	*out = *in
	if in.NumRetries != nil {
		in, out := &in.NumRetries, &out.NumRetries
		*out = new(int32)
		**out = **in
	}
	if in.NumAttemptsPerPriority != nil {
		in, out := &in.NumAttemptsPerPriority, &out.NumAttemptsPerPriority
		*out = new(int32)
		**out = **in
	}
	if in.RetryOn != nil {
		in, out := &in.RetryOn, &out.RetryOn
		*out = new(RetryOn)
		(*in).DeepCopyInto(*out)
	}
	if in.PerRetry != nil {
		in, out := &in.PerRetry, &out.PerRetry
		*out = new(PerRetryPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Retry.
func (in *Retry) DeepCopy() *Retry {
	if in == nil {
		return nil
	}
	out := new(Retry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RetryOn) DeepCopyInto(out *RetryOn) {
	*out = *in
	if in.Triggers != nil {
		in, out := &in.Triggers, &out.Triggers
		*out = make([]TriggerEnum, len(*in))
		copy(*out, *in)
	}
	if in.HTTPStatusCodes != nil {
		in, out := &in.HTTPStatusCodes, &out.HTTPStatusCodes
		*out = make([]HTTPStatus, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RetryOn.
func (in *RetryOn) DeepCopy() *RetryOn {
	if in == nil {
		return nil
	}
	out := new(RetryOn)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecurityPolicy) DeepCopyInto(out *SecurityPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecurityPolicy.
func (in *SecurityPolicy) DeepCopy() *SecurityPolicy {
	if in == nil {
		return nil
	}
	out := new(SecurityPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *SecurityPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecurityPolicyList) DeepCopyInto(out *SecurityPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]SecurityPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecurityPolicyList.
func (in *SecurityPolicyList) DeepCopy() *SecurityPolicyList {
	if in == nil {
		return nil
	}
	out := new(SecurityPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *SecurityPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecurityPolicySpec) DeepCopyInto(out *SecurityPolicySpec) {
	*out = *in
	in.PolicyTargetReferences.DeepCopyInto(&out.PolicyTargetReferences)
	if in.APIKeyAuth != nil {
		in, out := &in.APIKeyAuth, &out.APIKeyAuth
		*out = new(APIKeyAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.CORS != nil {
		in, out := &in.CORS, &out.CORS
		*out = new(CORS)
		(*in).DeepCopyInto(*out)
	}
	if in.BasicAuth != nil {
		in, out := &in.BasicAuth, &out.BasicAuth
		*out = new(BasicAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.JWT != nil {
		in, out := &in.JWT, &out.JWT
		*out = new(JWT)
		(*in).DeepCopyInto(*out)
	}
	if in.OIDC != nil {
		in, out := &in.OIDC, &out.OIDC
		*out = new(OIDC)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtAuth != nil {
		in, out := &in.ExtAuth, &out.ExtAuth
		*out = new(ExtAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.Authorization != nil {
		in, out := &in.Authorization, &out.Authorization
		*out = new(Authorization)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecurityPolicySpec.
func (in *SecurityPolicySpec) DeepCopy() *SecurityPolicySpec {
	if in == nil {
		return nil
	}
	out := new(SecurityPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Session) DeepCopyInto(out *Session) {
	*out = *in
	if in.Resumption != nil {
		in, out := &in.Resumption, &out.Resumption
		*out = new(SessionResumption)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Session.
func (in *Session) DeepCopy() *Session {
	if in == nil {
		return nil
	}
	out := new(Session)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SessionResumption) DeepCopyInto(out *SessionResumption) {
	*out = *in
	if in.Stateless != nil {
		in, out := &in.Stateless, &out.Stateless
		*out = new(StatelessTLSSessionResumption)
		**out = **in
	}
	if in.Stateful != nil {
		in, out := &in.Stateful, &out.Stateful
		*out = new(StatefulTLSSessionResumption)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SessionResumption.
func (in *SessionResumption) DeepCopy() *SessionResumption {
	if in == nil {
		return nil
	}
	out := new(SessionResumption)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ShutdownConfig) DeepCopyInto(out *ShutdownConfig) {
	*out = *in
	if in.DrainTimeout != nil {
		in, out := &in.DrainTimeout, &out.DrainTimeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.MinDrainDuration != nil {
		in, out := &in.MinDrainDuration, &out.MinDrainDuration
		*out = new(metav1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ShutdownConfig.
func (in *ShutdownConfig) DeepCopy() *ShutdownConfig {
	if in == nil {
		return nil
	}
	out := new(ShutdownConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ShutdownManager) DeepCopyInto(out *ShutdownManager) {
	*out = *in
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ShutdownManager.
func (in *ShutdownManager) DeepCopy() *ShutdownManager {
	if in == nil {
		return nil
	}
	out := new(ShutdownManager)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SlowStart) DeepCopyInto(out *SlowStart) {
	*out = *in
	if in.Window != nil {
		in, out := &in.Window, &out.Window
		*out = new(metav1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SlowStart.
func (in *SlowStart) DeepCopy() *SlowStart {
	if in == nil {
		return nil
	}
	out := new(SlowStart)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SourceMatch) DeepCopyInto(out *SourceMatch) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(SourceMatchType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SourceMatch.
func (in *SourceMatch) DeepCopy() *SourceMatch {
	if in == nil {
		return nil
	}
	out := new(SourceMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatefulTLSSessionResumption) DeepCopyInto(out *StatefulTLSSessionResumption) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatefulTLSSessionResumption.
func (in *StatefulTLSSessionResumption) DeepCopy() *StatefulTLSSessionResumption {
	if in == nil {
		return nil
	}
	out := new(StatefulTLSSessionResumption)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatelessTLSSessionResumption) DeepCopyInto(out *StatelessTLSSessionResumption) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatelessTLSSessionResumption.
func (in *StatelessTLSSessionResumption) DeepCopy() *StatelessTLSSessionResumption {
	if in == nil {
		return nil
	}
	out := new(StatelessTLSSessionResumption)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatusCodeMatch) DeepCopyInto(out *StatusCodeMatch) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(StatusCodeValueType)
		**out = **in
	}
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(int)
		**out = **in
	}
	if in.Range != nil {
		in, out := &in.Range, &out.Range
		*out = new(StatusCodeRange)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatusCodeMatch.
func (in *StatusCodeMatch) DeepCopy() *StatusCodeMatch {
	if in == nil {
		return nil
	}
	out := new(StatusCodeMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatusCodeRange) DeepCopyInto(out *StatusCodeRange) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatusCodeRange.
func (in *StatusCodeRange) DeepCopy() *StatusCodeRange {
	if in == nil {
		return nil
	}
	out := new(StatusCodeRange)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StringMatch) DeepCopyInto(out *StringMatch) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(StringMatchType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StringMatch.
func (in *StringMatch) DeepCopy() *StringMatch {
	if in == nil {
		return nil
	}
	out := new(StringMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPActiveHealthChecker) DeepCopyInto(out *TCPActiveHealthChecker) {
	*out = *in
	if in.Send != nil {
		in, out := &in.Send, &out.Send
		*out = new(ActiveHealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
	if in.Receive != nil {
		in, out := &in.Receive, &out.Receive
		*out = new(ActiveHealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPActiveHealthChecker.
func (in *TCPActiveHealthChecker) DeepCopy() *TCPActiveHealthChecker {
	if in == nil {
		return nil
	}
	out := new(TCPActiveHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPClientTimeout) DeepCopyInto(out *TCPClientTimeout) {
	*out = *in
	if in.IdleTimeout != nil {
		in, out := &in.IdleTimeout, &out.IdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPClientTimeout.
func (in *TCPClientTimeout) DeepCopy() *TCPClientTimeout {
	if in == nil {
		return nil
	}
	out := new(TCPClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPKeepalive) DeepCopyInto(out *TCPKeepalive) {
	*out = *in
	if in.Probes != nil {
		in, out := &in.Probes, &out.Probes
		*out = new(uint32)
		**out = **in
	}
	if in.IdleTime != nil {
		in, out := &in.IdleTime, &out.IdleTime
		*out = new(v1.Duration)
		**out = **in
	}
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPKeepalive.
func (in *TCPKeepalive) DeepCopy() *TCPKeepalive {
	if in == nil {
		return nil
	}
	out := new(TCPKeepalive)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPTimeout) DeepCopyInto(out *TCPTimeout) {
	*out = *in
	if in.ConnectTimeout != nil {
		in, out := &in.ConnectTimeout, &out.ConnectTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPTimeout.
func (in *TCPTimeout) DeepCopy() *TCPTimeout {
	if in == nil {
		return nil
	}
	out := new(TCPTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSSettings) DeepCopyInto(out *TLSSettings) {
	*out = *in
	if in.MinVersion != nil {
		in, out := &in.MinVersion, &out.MinVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.MaxVersion != nil {
		in, out := &in.MaxVersion, &out.MaxVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.Ciphers != nil {
		in, out := &in.Ciphers, &out.Ciphers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ECDHCurves != nil {
		in, out := &in.ECDHCurves, &out.ECDHCurves
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SignatureAlgorithms != nil {
		in, out := &in.SignatureAlgorithms, &out.SignatureAlgorithms
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ALPNProtocols != nil {
		in, out := &in.ALPNProtocols, &out.ALPNProtocols
		*out = make([]ALPNProtocol, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSSettings.
func (in *TLSSettings) DeepCopy() *TLSSettings {
	if in == nil {
		return nil
	}
	out := new(TLSSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetSelector) DeepCopyInto(out *TargetSelector) {
	*out = *in
	if in.Group != nil {
		in, out := &in.Group, &out.Group
		*out = new(v1.Group)
		**out = **in
	}
	if in.MatchLabels != nil {
		in, out := &in.MatchLabels, &out.MatchLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.MatchExpressions != nil {
		in, out := &in.MatchExpressions, &out.MatchExpressions
		*out = make([]metav1.LabelSelectorRequirement, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetSelector.
func (in *TargetSelector) DeepCopy() *TargetSelector {
	if in == nil {
		return nil
	}
	out := new(TargetSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Timeout) DeepCopyInto(out *Timeout) {
	*out = *in
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPTimeout)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Timeout.
func (in *Timeout) DeepCopy() *Timeout {
	if in == nil {
		return nil
	}
	out := new(Timeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Tracing) DeepCopyInto(out *Tracing) {
	*out = *in
	if in.SamplingFraction != nil {
		in, out := &in.SamplingFraction, &out.SamplingFraction
		*out = new(v1.Fraction)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomTags != nil {
		in, out := &in.CustomTags, &out.CustomTags
		*out = make(map[string]CustomTag, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Tracing.
func (in *Tracing) DeepCopy() *Tracing {
	if in == nil {
		return nil
	}
	out := new(Tracing)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TracingProvider) DeepCopyInto(out *TracingProvider) {
	*out = *in
	in.BackendCluster.DeepCopyInto(&out.BackendCluster)
	if in.Host != nil {
		in, out := &in.Host, &out.Host
		*out = new(string)
		**out = **in
	}
	if in.Zipkin != nil {
		in, out := &in.Zipkin, &out.Zipkin
		*out = new(ZipkinTracingProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TracingProvider.
func (in *TracingProvider) DeepCopy() *TracingProvider {
	if in == nil {
		return nil
	}
	out := new(TracingProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UnixSocket) DeepCopyInto(out *UnixSocket) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UnixSocket.
func (in *UnixSocket) DeepCopy() *UnixSocket {
	if in == nil {
		return nil
	}
	out := new(UnixSocket)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Wasm) DeepCopyInto(out *Wasm) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.RootID != nil {
		in, out := &in.RootID, &out.RootID
		*out = new(string)
		**out = **in
	}
	in.Code.DeepCopyInto(&out.Code)
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(apiextensionsv1.JSON)
		(*in).DeepCopyInto(*out)
	}
	if in.FailOpen != nil {
		in, out := &in.FailOpen, &out.FailOpen
		*out = new(bool)
		**out = **in
	}
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = new(WasmEnv)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Wasm.
func (in *Wasm) DeepCopy() *Wasm {
	if in == nil {
		return nil
	}
	out := new(Wasm)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WasmCodeSource) DeepCopyInto(out *WasmCodeSource) {
	*out = *in
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPWasmCodeSource)
		(*in).DeepCopyInto(*out)
	}
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(ImageWasmCodeSource)
		(*in).DeepCopyInto(*out)
	}
	if in.PullPolicy != nil {
		in, out := &in.PullPolicy, &out.PullPolicy
		*out = new(ImagePullPolicy)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WasmCodeSource.
func (in *WasmCodeSource) DeepCopy() *WasmCodeSource {
	if in == nil {
		return nil
	}
	out := new(WasmCodeSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WasmCodeSourceTLSConfig) DeepCopyInto(out *WasmCodeSourceTLSConfig) {
	*out = *in
	in.CACertificateRef.DeepCopyInto(&out.CACertificateRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WasmCodeSourceTLSConfig.
func (in *WasmCodeSourceTLSConfig) DeepCopy() *WasmCodeSourceTLSConfig {
	if in == nil {
		return nil
	}
	out := new(WasmCodeSourceTLSConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WasmEnv) DeepCopyInto(out *WasmEnv) {
	*out = *in
	if in.HostKeys != nil {
		in, out := &in.HostKeys, &out.HostKeys
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WasmEnv.
func (in *WasmEnv) DeepCopy() *WasmEnv {
	if in == nil {
		return nil
	}
	out := new(WasmEnv)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *XDSTranslatorHooks) DeepCopyInto(out *XDSTranslatorHooks) {
	*out = *in
	if in.Pre != nil {
		in, out := &in.Pre, &out.Pre
		*out = make([]XDSTranslatorHook, len(*in))
		copy(*out, *in)
	}
	if in.Post != nil {
		in, out := &in.Post, &out.Post
		*out = make([]XDSTranslatorHook, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new XDSTranslatorHooks.
func (in *XDSTranslatorHooks) DeepCopy() *XDSTranslatorHooks {
	if in == nil {
		return nil
	}
	out := new(XDSTranslatorHooks)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *XForwardedClientCert) DeepCopyInto(out *XForwardedClientCert) {
	*out = *in
	if in.Mode != nil {
		in, out := &in.Mode, &out.Mode
		*out = new(XFCCForwardMode)
		**out = **in
	}
	if in.CertDetailsToAdd != nil {
		in, out := &in.CertDetailsToAdd, &out.CertDetailsToAdd
		*out = make([]XFCCCertData, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new XForwardedClientCert.
func (in *XForwardedClientCert) DeepCopy() *XForwardedClientCert {
	if in == nil {
		return nil
	}
	out := new(XForwardedClientCert)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *XForwardedForSettings) DeepCopyInto(out *XForwardedForSettings) {
	*out = *in
	if in.NumTrustedHops != nil {
		in, out := &in.NumTrustedHops, &out.NumTrustedHops
		*out = new(uint32)
		**out = **in
	}
	if in.TrustedCIDRs != nil {
		in, out := &in.TrustedCIDRs, &out.TrustedCIDRs
		*out = make([]CIDR, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new XForwardedForSettings.
func (in *XForwardedForSettings) DeepCopy() *XForwardedForSettings {
	if in == nil {
		return nil
	}
	out := new(XForwardedForSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ZipkinTracingProvider) DeepCopyInto(out *ZipkinTracingProvider) {
	*out = *in
	if in.Enable128BitTraceID != nil {
		in, out := &in.Enable128BitTraceID, &out.Enable128BitTraceID
		*out = new(bool)
		**out = **in
	}
	if in.DisableSharedSpanContext != nil {
		in, out := &in.DisableSharedSpanContext, &out.DisableSharedSpanContext
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ZipkinTracingProvider.
func (in *ZipkinTracingProvider) DeepCopy() *ZipkinTracingProvider {
	if in == nil {
		return nil
	}
	out := new(ZipkinTracingProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ZoneAware) DeepCopyInto(out *ZoneAware) {
	*out = *in
	if in.PreferLocal != nil {
		in, out := &in.PreferLocal, &out.PreferLocal
		*out = new(PreferLocalZone)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ZoneAware.
func (in *ZoneAware) DeepCopy() *ZoneAware {
	if in == nil {
		return nil
	}
	out := new(ZoneAware)
	in.DeepCopyInto(out)
	return out
}
