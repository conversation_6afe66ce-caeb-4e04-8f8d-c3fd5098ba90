apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-host-override-metadata
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /metadata-override
    backendRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-host-override-metadata
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-host-override-metadata
  namespace: gateway-conformance-infra
spec:
  type: HostOverride
  hostOverrideSettings:
    overrideHostSources:
    - metadata:
        key: "envoy.lb"
        path:
        - key: "override-host"
  endpoints:
    - fqdn:
        hostname: infra-backend-v1.gateway-conformance-infra.svc.cluster.local
        port: 8080
