<!doctype html>
<html lang="{{ .Site.Language.Lang }}" class="no-js">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta http-equiv="x-ua-compatible" content="ie=edge">

  {{/* SEO meta tags */}}
  <title>{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }} | {{ .Site.Title }}{{ end }}</title>
  <meta name="description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}">

  {{/* Open Graph meta tags */}}
  <meta property="og:title" content="{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }}{{ end }}">
  <meta property="og:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}">
  <meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}">
  <meta property="og:url" content="{{ .Permalink }}">

  {{/* Bootstrap CSS from CDN for reliable styling */}}
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  {{/* Custom project styles */}}
  {{ $projectStyles := resources.Get "scss/_styles_project.scss" | css.Sass }}
  <link rel="stylesheet" href="{{ $projectStyles.RelPermalink }}">


</head>
<body class="td-{{ .Kind }}">
  <!-- Include site navbar -->
  {{ partial "navbar.html" . }}

  <div class="tool-container">
    <main role="main" class="tool-content">
      {{ block "main" . }}
        {{ .Content }}
      {{ end }}
    </main>
  </div>

  <!-- Include site footer -->
  {{ partial "footer.html" . }}

  {{/* Bootstrap JS from CDN */}}
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Include any additional JavaScript needed for the Tools -->
  {{ if .Params.includeBenchmark }}
    <script type="module" src="{{ "/js/benchmark-dashboard.js" | relURL }}"></script>
  {{ end }}
</body>
</html>
