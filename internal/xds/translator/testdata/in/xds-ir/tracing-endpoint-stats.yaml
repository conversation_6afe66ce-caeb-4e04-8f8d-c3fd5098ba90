name: "tracing"
metrics:
  enablePerEndpointStats: true
tracing:
  serviceName: "fake-name.fake-ns"
  samplingRate: 90
  customTags:
    "literal1":
      type: Literal
      literal:
        value: "value1"
    "env1":
      type: Environment
      environment:
        name: "env1"
        defaultValue: "-"
    "req1":
      type: RequestHeader
      requestHeader:
        name: "X-Request-Id"
        defaultValue: "-"
  destination:
    name: "tracing-0"
    authority: "otel-collector.default.svc.cluster.local"
    settings:
      - endpoints:
          - host: "otel-collector.default.svc.cluster.local"
            port: 4317
        protocol: "GRPC"
        addressType: FQDN
        name: "tracing-0/backend/0"
  provider:
    host: otel-collector.monitoring.svc.cluster.local
    port: 4317
    type: OpenTelemetry
http:
  - name: "first-listener"
    address: "::"
    port: 10080
    hostnames:
      - "*"
    path:
      mergeSlashes: true
      escapedSlashesAction: UnescapeAndRedirect
    routes:
      - name: "direct-route"
        hostname: "*"
        destination:
          name: "direct-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "direct-route-dest/backend/0"
