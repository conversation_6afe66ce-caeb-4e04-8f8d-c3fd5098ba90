- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadAssignment:
    clusterName: first-route-dest
    endpoints:
    - loadBalancingWeight: 1
      locality:
        region: backend-hosts-override-header
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: target-pod
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadAssignment:
    clusterName: second-route-dest
    endpoints:
    - loadBalancingWeight: 1
      locality:
        region: backend-hosts-override-metadata
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - metadata:
              key: envoy.lb
              path:
              - key: target-pod
  name: second-route-dest
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadAssignment:
    clusterName: third-route-dest
    endpoints:
    - loadBalancingWeight: 1
      locality:
        region: backend-hosts-override-multiple
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: target-pod
          - header: route-strategy
          - metadata:
              key: envoy.lb
              path:
              - key: override-host
  name: third-route-dest
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
