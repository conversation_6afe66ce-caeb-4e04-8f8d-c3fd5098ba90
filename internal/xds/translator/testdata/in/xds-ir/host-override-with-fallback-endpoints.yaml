http:
- name: "first-listener"
  address: "0.0.0.0"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    pathMatch:
      exact: "/"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "fallback1.default.svc.cluster.local"
          port: 80
        - host: "fallback2.default.svc.cluster.local"
          port: 80
        name: backend-hosts-override-with-fallback
        protocol: HTTP
        weight: 1
        isHostOverride: true
        hostOverrideConfig:
          overrideHostSources:
          - header: "target-pod"
        addressType: FQDN
  - name: "second-route"
    hostname: "*"
    pathMatch:
      exact: "/metadata"
    destination:
      name: "second-route-dest"
      settings:
      - endpoints:
        - host: "fallback3.default.svc.cluster.local"
          port: 8080
        - host: "fallback4.default.svc.cluster.local"
          port: 8080
        name: backend-hosts-override-metadata-with-fallback
        protocol: HTTP
        weight: 1
        isHostOverride: true
        hostOverrideConfig:
          overrideHostSources:
          - metadata:
              key: "envoy.lb"
              path:
              - key: "target-pod"
        addressType: FQDN
