apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/component: proxy
    app.kubernetes.io/managed-by: envoy-gateway
    app.kubernetes.io/name: envoy
    gateway.envoyproxy.io/owning-gateway-name: gateway-1
    gateway.envoyproxy.io/owning-gateway-namespace: ns1
    gateway.networking.k8s.io/gateway-name: gateway-1
  name: gateway-1
  namespace: ns1
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-1
    uid: test-owner-reference-uid-for-gateway
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: proxy
      app.kubernetes.io/managed-by: envoy-gateway
      app.kubernetes.io/name: envoy
      gateway.envoyproxy.io/owning-gateway-name: gateway-1
      gateway.envoyproxy.io/owning-gateway-namespace: ns1
      gateway.networking.k8s.io/gateway-name: gateway-1
status:
  currentHealthy: 0
  desiredHealthy: 0
  disruptionsAllowed: 0
  expectedPods: 0
