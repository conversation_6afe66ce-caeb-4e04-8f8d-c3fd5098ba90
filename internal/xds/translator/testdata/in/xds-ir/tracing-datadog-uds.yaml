name: "tracing"
tracing:
  serviceName: "fake-name.fake-ns"
  samplingRate: 90
  customTags:
    "literal1":
      type: Literal
      literal:
        value: "value1"
    "env1":
      type: Environment
      environment:
        name: "env1"
        defaultValue: "-"
    "req1":
      type: RequestHeader
      requestHeader:
        name: "X-Request-Id"
        defaultValue: "-"
  authority: "datadog-agent.default.svc.cluster.local"
  destination:
    name: "tracing-0"
    settings:
      - addressType: IP
        endpoints:
          - host: ""
            path: /var/run/envoy-uds/dsd.socket
            port: 0
        name: "tracing-0/backend/0"
  provider:
    type: Datadog
http:
  - name: "first-listener"
    address: "::"
    port: 10080
    hostnames:
      - "*"
    path:
      mergeSlashes: true
      escapedSlashesAction: UnescapeAndRedirect
    routes:
      - name: "direct-route"
        hostname: "*"
        destination:
          name: "direct-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "direct-route-dest/backend/0"
