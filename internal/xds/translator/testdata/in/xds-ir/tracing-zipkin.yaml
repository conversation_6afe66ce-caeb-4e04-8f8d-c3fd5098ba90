name: "tracing"
tracing:
  serviceName: "fake-name.fake-ns"
  samplingRate: 90
  customTags:
    "literal1":
      type: Literal
      literal:
        value: "value1"
    "env1":
      type: Environment
      environment:
        name: "env1"
        defaultValue: "-"
    "req1":
      type: RequestHeader
      requestHeader:
        name: "X-Request-Id"
        defaultValue: "-"
  authority: "zipkin.default.svc.cluster.local"
  destination:
    name: "tracing-0"
    settings:
    - endpoints:
      - host: "zipkin.default.svc.cluster.local"
        port: 9411
      protocol: "TCP"
      addressType: FQDN
      name: "tracing-0/backend/0"
  provider:
    host: zipkin.default.svc.cluster.local
    port: 9411
    type: Zipkin
    zipkin:
      enable128BitTraceId: true
      disableSharedSpanContext: true
http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "direct-route"
    hostname: "*"
    destination:
      name: "direct-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "direct-route-dest/backend/0"
