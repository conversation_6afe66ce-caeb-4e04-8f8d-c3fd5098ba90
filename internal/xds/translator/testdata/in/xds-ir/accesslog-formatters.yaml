name: "accesslog"
accesslog:
  text:
  - path: "/dev/stdout"
    format: |
      [%START_TIME%] "%CEL(request.method)% %CEL(request.path)% %CEL(request.protocol)%" %CEL(response.code)% %CEL(response.flags)% %CEL(request.total_size)% %CEL(response.total_size)% %CEL(request.duration)% %CEL(response.headers['X-ENVOY-UPSTREAM-SERVICE-TIME'])% "%CEL(request.headers['X-FORWARDED-FOR'])%" "%CEL(request.useragent)%" "%CEL(request.id)%" "%CEL(request.host)%" "%CEL(upstream.address)%"
  json:
  - path: "/dev/stdout"
    json:
      start_time: "%START_TIME%"
      method: "%REQ(:METHOD)%"
      path: "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%"
      protocol: "%PROTOCOL%"
      response_code: "%RESPONSE_CODE%"
      test_key: "%METADATA(DYNAMIC:com.test.my_filter:test_key)%"
  - path: "/dev/stdout"
    json:
      start_time: "%START_TIME%"
      method: "%REQ(:METHOD)%"
      path_without_query: "%REQ_WITHOUT_QUERY(X-ENVOY-ORIGINAL-PATH?:PATH)%"
      protocol: "%PROTOCOL%"
      response_code: "%RESPONSE_CODE%"
  openTelemetry:
  - text: |
      [%START_TIME%] "%REQ(:METHOD)% %REQ_WITHOUT_QUERY(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"
    attributes:
      "request_method": "%CEL(request.method)%"
      "response_code": "%RESPONSE_CODE%"
      "test_key": "%METADATA(DYNAMIC:com.test.my_filter:test_key)%"
    resources:
      "cluster_name": "cluster1"
    authority: "otel-collector.default.svc.cluster.local"
    destination:
      name: "accesslog-0"
      settings:
      - endpoints:
        - host: "otel-collector.default.svc.cluster.local"
          port: 4317
        addressType: FQDN
        protocol: "GRPC"
        name: "accesslog-0/backend/0"
http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "direct-route"
    hostname: "*"
    destination:
      name: "direct-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "direct-route-dest/backend/0"
