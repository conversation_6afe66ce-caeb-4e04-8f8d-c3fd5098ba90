name: "accesslog"
accesslog:
  text:
  - path: "/dev/stdout"
    format: |
      this is a brand new log
  json:
  - path: "/dev/stdout"
    json:
      start_time: "%START_TIME%"
      method: "%REQ(:METHOD)%"
      path: "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%"
      protocol: "%PROTOCOL%"
      response_code: "%RESPONSE_CODE%"
  als:
  - name: als
    destination:
      name: accesslog/monitoring/envoy-als/port/9000
      settings:
      - addressType: IP
        endpoints:
        - host: *******
          port: 9000
        protocol: GRPC
        weight: 1
    http:
      requestHeaders:
      - x-client-ip-address
      responseHeaders:
      - cache-control
      responseTrailers:
      - expires
    type: HTTP
  openTelemetry:
  - text: |
      this is a brand new log
    attributes:
      "response_code": "%RESPONSE_CODE%"
    resources:
      "cluster_name": "cluster1"
    authority: "otel-collector.default.svc.cluster.local"
    destination:
      name: "accesslog-0"
      settings:
      - endpoints:
        - host: "otel-collector.default.svc.cluster.local"
          port: 4317
        addressType: FQDN
        protocol: "GRPC"
http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "direct-route"
    hostname: "*"
    destination:
      name: "direct-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
