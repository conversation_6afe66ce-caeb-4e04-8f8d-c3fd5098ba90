backendTLSPolicies:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: BackendTLSPolicy
  metadata:
    creationTimestamp: null
    generation: 10
    name: policy-btls
    namespace: policies
  spec:
    targetRefs:
    - group: ""
      kind: Service
      name: http-backend
      sectionName: http
    validation:
      caCertificateRefs:
      - group: ""
        kind: ConfigMap
        name: ca-cmap
      hostname: example.com
  status:
    ancestors: null
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-btls
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-btls
    namespace: envoy-gateway
  spec:
    parentRefs:
    - name: gateway-btls
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: http-backend
        namespace: backends
        port: 8080
      matches:
      - path:
          type: Exact
          value: /exact
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-btls
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-btls:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-btls/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-btls
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-btls
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-btls:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-btls
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-btls/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-btls
            namespace: envoy-gateway
          name: httproute/envoy-gateway/httproute-btls/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: ***********
              port: 8080
            metadata:
              kind: Service
              name: http-backend
              namespace: backends
              sectionName: "8080"
            name: httproute/envoy-gateway/httproute-btls/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-btls
          namespace: envoy-gateway
        name: httproute/envoy-gateway/httproute-btls/rule/0/match/0/*
        pathMatch:
          distinct: false
          exact: /exact
          name: ""
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
