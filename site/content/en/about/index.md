+++
title = "About Envoy Gateway"
linktitle = "About"
+++

{{% blocks/cover title="Advanced Traffic Management for Everyone" height="auto" color="primary" %}}
<div class="container">
<p class="lead">
Envoy Gateway democratizes the power of <a href="https://www.envoyproxy.io/">Envoy Proxy</a>, bringing you it's battle-tested traffic management via a Kubernetes Gateway API implementation. Envoy is trusted by tech giants worldwide, we're making enterprise capabilities accessible to everyone.
</p>
<div class="mt-5">
<a class="btn btn-lg btn-glass me-3" href="/docs/tasks/quickstart/">
<i class="fas fa-rocket me-2"></i>Get Started
</a>
<a class="btn btn-lg btn-glass" href="https://github.com/envoyproxy/gateway">
<i class="fab fa-github me-2"></i>GitHub
</a>
</div>
</div>
{{% /blocks/cover %}}

{{% blocks/section color="white" %}}
<div class="row justify-content-center">
<div class="col-md-10">
<h2 class="text-center mb-5">Our Vision</h2>
<p class="text-center mb-5 lead">
Envoy Gateway was created with a clear mission: to make Envoy Proxy's powerful features accessible to all users while providing a robust implementation of the Kubernetes Gateway API. We believe that managing application
traffic should be straightforward, allowing teams to focus on building great applications rather than wrestling with complex proxy configurations.
</p>

</div>
{{% /blocks/section %}}

{{% blocks/section color="light" %}}
<div class="col-12">
<h2 class="text-center mb-5">Why Choose Envoy Gateway?</h2>
</div>

<div class="feature-grid">
  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-shield-alt"></i>
    </div>
    <h3>A Battle-Tested Foundation</h3>
    <p>By using Envoy Gateway, you're using Envoy Proxy's proven architecture that powers mission-critical systems at leading cloud providers and companies worldwide. When reliability at scale matters, trust the proxy that's been hardened by the world's most demanding network traffic environments.</p>
  </div>

  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-cube"></i>
    </div>
    <h3>Implements and Extends the Kubernetes Gateway API </h3>
    <p>Experience seamless integration with Kubernetes through the Gateway API implementation, and . Manage traffic with modern, standardized patterns that align perfectly with cloud native best practices.</p>
  </div>

  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-puzzle-piece"></i>
    </div>
    <h3>Extensible Architecture</h3>
    <p>The Envoy Gateway control plane is designed for extensibility, just as the Envoy Proxy data plane. Enabling you to create custom policies, specialized solutions, and a growing ecosystem of extensions. Build and integrate exactly what your organization needs.</p>
  </div>
</div>
{{% /blocks/section %}}



{{% blocks/section color="light" %}}
<div class="col-12">
<h2 class="text-center mb-5">Join Our Community</h2>
</div>

<div class="feature-grid">
  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-book"></i>
    </div>
    <h3>Start Building</h3>
    <p>Dive into our <a href="/docs/">comprehensive documentation</a> and start building with enterprise-grade traffic management capabilities.</p>
  </div>

  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-users"></i>
    </div>
    <h3>Connect & Contribute</h3>
    <p>Join our thriving community of developers and contribute to shaping the future of cloud native networking.</p>
  </div>

  <div class="feature-card">
    <div class="icon-container">
      <i class="fas fa-lightbulb"></i>
    </div>
    <h3>Share & Learn</h3>
    <p>Share your experiences, learn from others, and help us improve Envoy Gateway for everyone.</p>
  </div>
</div>
{{% /blocks/section %}}

{{% blocks/section color="white" %}}
<div class="cncf-section">
  <img src="/img/cncf.svg" alt="Cloud Native Computing Foundation" width="300">
  <p class="mt-4">
    A proud member of the <a href="https://www.envoyproxy.io/">Envoy Proxy</a> family, backed by the Cloud Native Computing Foundation. Together, we're building the future of cloud native networking.
  </p>
</div>
{{% /blocks/section %}}
