- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadAssignment:
    clusterName: first-route-dest
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: fallback1.default.svc.cluster.local
              portValue: 80
        loadBalancingWeight: 1
      - endpoint:
          address:
            socketAddress:
              address: fallback2.default.svc.cluster.local
              portValue: 80
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: backend-hosts-override-with-fallback
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: target-pod
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  ignoreHealthOnHostRemoval: true
  lbPolicy: CLUSTER_PROVIDED
  loadAssignment:
    clusterName: second-route-dest
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: fallback3.default.svc.cluster.local
              portValue: 8080
        loadBalancingWeight: 1
      - endpoint:
          address:
            socketAddress:
              address: fallback4.default.svc.cluster.local
              portValue: 8080
        loadBalancingWeight: 1
      loadBalancingWeight: 1
      locality:
        region: backend-hosts-override-metadata-with-fallback
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - metadata:
              key: envoy.lb
              path:
              - key: target-pod
  name: second-route-dest
  perConnectionBufferLimitBytes: 32768
  type: STRICT_DNS
