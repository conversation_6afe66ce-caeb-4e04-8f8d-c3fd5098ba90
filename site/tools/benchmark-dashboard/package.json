{"name": "benchmark-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 8080", "build": "npm run build:css:once && vite build && npm run postbuild", "build:css:once": "tailwindcss -i ./src/index.css -o ./dist/benchmark-dashboard.css", "build:css": "tailwindcss -i ./src/index.css -o ./dist/benchmark-dashboard.css --watch", "postbuild": "cp dist/*.js ../../static/js/ && cp dist/*.css ../../static/css/", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.62.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.13.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.1", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.17.0", "vite": "^5.4.19"}}