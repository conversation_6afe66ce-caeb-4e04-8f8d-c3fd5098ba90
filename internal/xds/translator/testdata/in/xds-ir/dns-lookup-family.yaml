accessLog:
  openTelemetry:
    - destination:
        name: "accesslog_otel_0_1"
        settings:
          - endpoints:
              - host: "otel-collector.default.svc.cluster.local"
                port: 4317
            protocol: "GRPC"
            addressType: FQDN
            name: "accesslog-0/backend/0"
      resources:
        k8s.cluster.name: cluster-1
      text: |
        [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
      traffic:
        dns:
          dnsRefreshRate: 30s
          lookupFamily: IPv4
  text:
    - format: |
        [%START_TIME%] "%REQ(:METHOD)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"\n
      path: /dev/stdout
http:
  - address: 0.0.0.0
    hostnames:
      - "*"
    isHTTP2: true
    metadata:
      kind: Gateway
      name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    name: envoy-gateway/gateway-1/http
    path:
      escapedSlashesAction: UnescapeAndRedirect
      mergeSlashes: true
    port: 10080
    routes:
      - destination:
          name: httproute/default/httproute-1/rule/0
          settings:
            - addressType: FQDN
              endpoints:
                - host: grpc-infra-backend.gateway-conformance-infra.svc.cluster.local
                  port: 8080
              protocol: HTTP
              weight: 1
        envoyExtensions:
          extProcs:
            - authority: backend-fqdn2.default:9090
              destination:
                name: envoyextensionpolicy/default/policy-for-httproute/extproc/0
                settings:
                  - addressType: FQDN
                    endpoints:
                      - host: backend-v2.gateway-conformance-infra.svc.cluster.local
                        port: 9090
                    protocol: GRPC
                    weight: 1
              name: envoyextensionpolicy/default/policy-for-httproute/extproc/0
              traffic:
                dns:
                  dnsRefreshRate: 5s
                  lookupFamily: IPv4AndIPv6
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /
        security:
          extAuth:
            bodyToExtAuth:
              maxRequestBytes: 8192
            failOpen: false
            http:
              authority: backend-v3.gateway-conformance-infra.svc.cluster.local:8080
              destination:
                name: securitypolicy/envoy-gateway/policy-for-gateway-1/extauth/0
                settings:
                  - addressType: FQDN
                    endpoints:
                      - host: backend-v3.gateway-conformance-infra.svc.cluster.local
                        port: 8080
                    protocol: HTTP
                    weight: 1
              path: ""
            name: securitypolicy/envoy-gateway/policy-for-gateway-1
        traffic:
          dns:
            dnsRefreshRate: 5s
            lookupFamily: IPv6
            respectDnsTtl: false
      - destination:
          name: grpcroute/default/grpcroute-1/rule/0
          settings:
            - addressType: FQDN
              endpoints:
                - host: grpc-infra-backend.gateway-conformance-infra.svc.cluster.local
                  port: 8080
              protocol: GRPC
              weight: 1
        hostname: "*"
        isHTTP2: true
        metadata:
          kind: GRPCRoute
          name: grpcroute-1
          namespace: default
        name: grpcroute/default/grpcroute-1/rule/0/match/-1/*
        security:
          extAuth:
            bodyToExtAuth:
              maxRequestBytes: 8192
            failOpen: false
            http:
              authority: backend-v3.gateway-conformance-infra.svc.cluster.local:8080
              destination:
                name: securitypolicy/envoy-gateway/policy-for-gateway-1/extauth/0
                settings:
                  - addressType: FQDN
                    endpoints:
                      - host: backend-v3.gateway-conformance-infra.svc.cluster.local
                        port: 8080
                    protocol: HTTP
                    weight: 1
              path: ""
            name: securitypolicy/envoy-gateway/policy-for-gateway-1
        traffic:
          dns:
            dnsRefreshRate: 5s
            lookupFamily: IPv6
            respectDnsTtl: false
readyListener:
  address: 0.0.0.0
  ipFamily: IPv4
  path: /ready
  port: 19003
